// // src/components/LoginPage.tsx
// import React, { useState } from 'react';
// import { LogIn, UserPlus, ArrowLeft } from 'lucide-react';
// import Image from 'next/image';

// // Interface for the component props
// interface LoginPageProps {
//   onLoginSuccess: () => void;
//   onBack: () => void;
// }

// const LoginPage: React.FC<LoginPageProps> = ({ onLoginSuccess, onBack }) => {
//   const [isRegistering, setIsRegistering] = useState(false);
//   const [email, setEmail] = useState('');
//   const [password, setPassword] = useState('');
//   const [confirmPassword, setConfirmPassword] = useState('');
//   const [errorMessage, setErrorMessage] = useState('');
//   const [successMessage, setSuccessMessage] = useState('');

//   // Handle form submission for both login and register
//   const handleSubmit = (e: React.FormEvent) => {
//     e.preventDefault();
//     setErrorMessage('');
//     setSuccessMessage('');

//     if (isRegistering) {
//       if (password !== confirmPassword) {
//         setErrorMessage("Passwords do not match.");
//         return;
//       }
//       // Mock registration logic
//       console.log('Registering with:', { email, password });
//       // In a real app, you would make an API call here.
//       // For this demo, we'll just log in after a fake "registration".
//       setSuccessMessage("Registration successful! You can now log in.");
//       setIsRegistering(false); // Redirect to login page
//       setEmail('');
//       setPassword('');
//       setConfirmPassword('');
//     } else {
//       // Mock login logic
//       console.log('Logging in with:', { email, password });
//       // In a real app, you would make an API call here.
//       // For this demo, we'll assume login is always successful.
//       onLoginSuccess();
//     }
//   };

//   return (
//     <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200 p-4">
//       <div className="w-full max-w-md bg-white rounded-3xl shadow-2xl overflow-hidden p-8 border border-gray-200 relative">
//         {/* Back Button */}
//         <button
//           onClick={onBack}
//           className="absolute top-6 left-6 text-gray-500 hover:text-gray-900 transition-colors flex items-center space-x-2 font-medium"
//         >
//           {/* <ArrowLeft className="w-5 h-5" /> */}
//           {/* <span>Back</span> */}
//         </button>

//         {/* Logo and Title */}
//         <div className="text-center mb-8 mt-12">
//           <Image
//             src="/logo.png"
//             alt="Logo"
//             width={120}
//             height={30}
//             className="mx-auto mb-4"
//           />
//           <h2 className="text-3xl font-bold text-gray-900">
//             {isRegistering ? 'Create an Account' : 'Welcome'}
//           </h2>
//           <p className="text-gray-500 text-sm mt-2">
//             {isRegistering ? 'Join to get started.' : ''}
//           </p>
//         </div>

//         {/* Tabbed Navigation */}
//         <div className="flex justify-center mb-6">
//           <button
//             onClick={() => setIsRegistering(false)}
//             className={`flex-1 py-3 px-4 text-center text-sm font-semibold rounded-t-lg transition-colors ${
//               !isRegistering ? 'bg-blue-600 text-white shadow-md' : 'bg-gray-200 text-gray-600 hover:bg-gray-300'
//             }`}
//           >
//             <div className="flex items-center justify-center space-x-2">
//               <LogIn className="w-4 h-4" />
//               <span>Login</span>
//             </div>
//           </button>
//           <button
//             onClick={() => setIsRegistering(true)}
//             className={`flex-1 py-3 px-4 text-center text-sm font-semibold rounded-t-lg transition-colors ${
//               isRegistering ? 'bg-blue-600 text-white shadow-md' : 'bg-gray-200 text-gray-600 hover:bg-gray-300'
//             }`}
//           >
//             <div className="flex items-center justify-center space-x-2">
//               <UserPlus className="w-4 h-4" />
//               <span>Register</span>
//             </div>
//           </button>
//         </div>

//         {/* Form */}
//         <form onSubmit={handleSubmit} className="space-y-6">
//           {errorMessage && (
//             <div className="p-3 bg-red-100 text-red-700 rounded-lg text-sm text-center">
//               {errorMessage}
//             </div>
//           )}
//           {successMessage && (
//             <div className="p-3 bg-green-100 text-green-700 rounded-lg text-sm text-center">
//               {successMessage}
//             </div>
//           )}
//           <div>
//             <label className="block text-sm font-medium text-gray-700 mb-1">Email address</label>
//             <input
//               type="email"
//               value={email}
//               onChange={(e) => setEmail(e.target.value)}
//               required
//               className="w-full px-4 py-3 rounded-lg border-2 border-gray-300 focus:border-blue-500 focus:ring-blue-500 focus:ring-1 transition-all duration-200"
//               placeholder="<EMAIL>"
//             />
//           </div>
//           <div>
//             <label className="block text-sm font-medium text-gray-700 mb-1">Password</label>
//             <input
//               type="password"
//               value={password}
//               onChange={(e) => setPassword(e.target.value)}
//               required
//               className="w-full px-4 py-3 rounded-lg border-2 border-gray-300 focus:border-blue-500 focus:ring-blue-500 focus:ring-1 transition-all duration-200"
//               placeholder="••••••••"
//             />
//           </div>
//           {isRegistering && (
//             <div>
//               <label className="block text-sm font-medium text-gray-700 mb-1">Confirm Password</label>
//               <input
//                 type="password"
//                 value={confirmPassword}
//                 onChange={(e) => setConfirmPassword(e.target.value)}
//                 required
//                 className="w-full px-4 py-3 rounded-lg border-2 border-gray-300 focus:border-blue-500 focus:ring-blue-500 focus:ring-1 transition-all duration-200"
//                 placeholder="••••••••"
//               />
//             </div>
//           )}
//           <button
//             type="submit"
//             className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 rounded-lg transition-colors shadow-lg hover:shadow-xl"
//           >
//             {isRegistering ? 'Register' : 'Login'}
//           </button>
//         </form>
//       </div>
//     </div>
//   );
// };

// export default LoginPage;

// src/components/LoginPage.tsx
import React, { useState } from 'react';
import { LogIn, UserPlus } from 'lucide-react';
import Image from 'next/image';

// Interface for the component props
interface LoginPageProps {
  onLoginSuccess: () => void;
}

const LoginPage: React.FC<LoginPageProps> = ({ onLoginSuccess }) => {
  const [isRegistering, setIsRegistering] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  // Handle form submission for both login and register
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setErrorMessage('');
    setSuccessMessage('');

    if (isRegistering) {
      // Logic for registration
      if (password !== confirmPassword) {
        setErrorMessage("Passwords do not match.");
        return;
      }
      // Mock registration logic
      console.log('Registering with:', { email, password });
      setSuccessMessage("Registration successful! Please log in.");
      setIsRegistering(false); // Switch back to login page
      setEmail('');
      setPassword('');
      setConfirmPassword('');
    } else {
      // Logic for login
      // Mock login logic, assuming successful login for this demo
      console.log('Logging in with:', { email, password });
      onLoginSuccess();
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200 p-4">
      <div className="w-full max-w-md bg-white rounded-3xl shadow-2xl overflow-hidden p-8 border border-gray-200 relative">
        {/* Logo and Title */}
        <div className="text-center mb-8 mt-4">
          <Image
            src="/logo.png"
            alt="Logo"
            width={120}
            height={30}
            className="mx-auto mb-4"
          />
          <h2 className="text-3xl font-bold text-gray-900">
            {isRegistering ? 'Create an Account' : 'Welcome'}
          </h2>
          <p className="text-gray-500 text-sm mt-2">
            {isRegistering ? 'Join to get started.' : 'Sign in to continue.'}
          </p>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {errorMessage && (
            <div className="p-3 bg-red-100 text-red-700 rounded-lg text-sm text-center">
              {errorMessage}
            </div>
          )}
          {successMessage && (
            <div className="p-3 bg-green-100 text-green-700 rounded-lg text-sm text-center">
              {successMessage}
            </div>
          )}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Email address</label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="w-full px-4 py-3 rounded-lg border-2 border-gray-300 focus:border-blue-500 focus:ring-blue-500 focus:ring-1 transition-all duration-200"
              placeholder="<EMAIL>"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Password</label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              className="w-full px-4 py-3 rounded-lg border-2 border-gray-300 focus:border-blue-500 focus:ring-blue-500 focus:ring-1 transition-all duration-200"
              placeholder="••••••••"
            />
          </div>
          {isRegistering && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Confirm Password</label>
              <input
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                required
                className="w-full px-4 py-3 rounded-lg border-2 border-gray-300 focus:border-blue-500 focus:ring-blue-500 focus:ring-1 transition-all duration-200"
                placeholder="••••••••"
              />
            </div>
          )}
          <button
            type="submit"
            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 rounded-lg transition-colors shadow-lg hover:shadow-xl"
          >
            {isRegistering ? 'Register' : 'Login'}
          </button>
        </form>

        {/* Toggle between login and register */}
        <div className="mt-6 text-center text-sm">
          {isRegistering ? (
            <p className="text-gray-600">
              Already have an account?{' '}
              <button
                type="button"
                onClick={() => {
                  setErrorMessage('');
                  setSuccessMessage('');
                  setIsRegistering(false);
                }}
                className="font-semibold text-blue-600 hover:underline"
              >
                Login here
              </button>
            </p>
          ) : (
            <p className="text-gray-600">
              New to the app?{' '}
              <button
                type="button"
                onClick={() => {
                  setErrorMessage('');
                  setSuccessMessage('');
                  setIsRegistering(true);
                }}
                className="font-semibold text-blue-600 hover:underline"
              >
                Register here
              </button>
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
