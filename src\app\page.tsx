// // src/app/page.tsx
// 'use client';

// import React, { useState } from 'react';
// import { Search, FileText, MessageCircle, Send, User, LogOut, ArrowLeft, Menu, X } from 'lucide-react';
// import Image from 'next/image';
// import ChatInterface from '../components/ChatInterface';
// import GeorgiaArticleSelector from '../components/GeorgiaArticleSelector';
// import LoginPage from '../components/LoginPage'; // Assuming this component exists

// // Type definitions
// interface Document {
//   id: number;
//   title: string;
//   type: string;
//   filename?: string;
// }
// interface GeorgiaDocument {
//   id: number;
//   title: string;
//   description: string;
//   filename: string;
//   icon: string;
//   color: string;
//   lightColor: string;
// }

// // Add the GeorgiaArticle interface for the article selector
// interface GeorgiaArticle {
//   id: number;
//   title: string;
//   subtitle: string;
//   filename: string;
//   sections: string;
//   icon: React.ReactNode;
//   color: string;
//   lightColor: string;
//   description: string;
// }
// interface Message {
//   id: number;
//   type: 'user' | 'ai';
//   content: string;
//   rating?: 'up' | 'down' | null;
// }
// type LawType = 'federal' | 'state' | 'county';

// // Updated to include the new article selection page
// type CurrentPage = 'landing' | 'stateSelection' | 'georgiaDocuments' | 'georgiaArticleSelection' | 'chat' | 'login';
// interface DocumentData {
//   [key: string]: Document[];
// }
// interface LandingPageProps {
//   onNavigate: (type: LawType) => void;
//   onGoHome: () => void; // New prop for navigating to home
//   onLogout: () => void; // ADDED: Now the LandingPage accepts an onLogout prop
// }
// interface StateSelectionPageProps {
//   onStateSelect: (stateName: string) => void;
//   onBack: () => void;
//   onGoHome: () => void; // New prop for navigating to home
//   onLogout: () => void;
// }
// interface GeorgiaDocumentSelectionProps {
//   onDocumentSelect: (document: GeorgiaDocument) => void;
//   onBack: () => void;
//   onGoHome: () => void; // New prop for navigating to home
//   onLogout: () => void;
// }

// // Mock document data for federal and county
// const documentData: DocumentData = {
//   federal: [
//     { id: 1, title: "Federal Election Campaign Act", type: "Federal Statute" },
//     { id: 2, title: "Voting Rights Act of 1965", type: "Federal Statute" },
//     { id: 3, title: "Help America Vote Act", type: "Federal Statute" },
//     { id: 4, title: "FEC Regulations 11 CFR", type: "Federal Regulation" }
//   ],
//   county: [
//     { id: 9, title: "GA County Election Procedures", type: "County Ordinance" },
//     { id: 10, title: "GA County Polling Guidelines", type: "County Policy" },
//     { id: 11, title: "GA County Ballot Design", type: "County Standard" },
//     { id: 12, title: "GA County Voter Outreach", type: "County Policy" }
//   ]
// };

// // Georgia-specific documents
// const georgiaDocuments: GeorgiaDocument[] = [
//   {
//     id: 1,
//     title: "Congressional Districts",
//     description: "Laws governing the establishment, modification, and administration of congressional district boundaries in Georgia.",
//     filename: "1.txt",
//     icon: "🗺️",
//     color: "from-blue-500 to-blue-600",
//     lightColor: "bg-blue-50 text-blue-800 border-blue-200"
//   },
//   {
//     id: 2,
//     title: "Elections & Election Systems",
//     description: "Comprehensive election procedures, voting systems, ballot requirements, and election administration in Georgia.",
//     filename: "2.txt",
//     icon: "🗳️",
//     color: "from-green-500 to-green-600",
//     lightColor: "bg-green-50 text-green-800 border-green-200"
//   },
//   {
//     id: 3,
//     title: "Recall of Public Officers",
//     description: "Legal procedures and requirements for the recall of elected officials and public officers in Georgia.",
//     filename: "3.txt",
//     icon: "⚖️",
//     color: "from-purple-500 to-purple-600",
//     lightColor: "bg-purple-50 text-purple-800 border-purple-200"
//   },
//   {
//     id: 4,
//     title: "Gov Transparency & Campaign Finance",
//     description: "Government transparency requirements, campaign finance regulations, and disclosure laws in Georgia.",
//     filename: "4.txt",
//     icon: "💰",
//     color: "from-orange-500 to-orange-600",
//     lightColor: "bg-orange-50 text-orange-800 border-orange-200"
//   }
// ];

// // US States data
// const US_STATES = [
//   'Alabama', 'Alaska', 'Arizona', 'Arkansas', 'California', 'Colorado',
//   'Connecticut', 'Delaware', 'Florida', 'Georgia', 'Hawaii', 'Idaho',
//   'Illinois', 'Indiana', 'Iowa', 'Kansas', 'Kentucky', 'Louisiana',
//   'Maine', 'Maryland', 'Massachusetts', 'Michigan', 'Minnesota',
//   'Mississippi', 'Missouri', 'Montana', 'Nebraska', 'Nevada',
//   'New Hampshire', 'New Jersey', 'New Mexico', 'New York',
//   'North Carolina', 'North Dakota', 'Ohio', 'Oklahoma', 'Oregon',
//   'Pennsylvania', 'Rhode Island', 'South Carolina', 'South Dakota',
//   'Tennessee', 'Texas', 'Utah', 'Vermont', 'Virginia', 'Washington',
//   'West Virginia', 'Wisconsin', 'Wyoming'
// ];

// const availableStates = ['Georgia'];

// // UPDATED: LandingPage now accepts onLogout prop
// const LandingPage: React.FC<LandingPageProps> = ({ onNavigate, onGoHome, onLogout }) => {
//   return (
//     <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-red-50">
//       {/* Header */}
//       <header className="flex justify-between items-center p-6">
//         {/* Logo with onClick handler */}
//         <div
//           className="flex items-center space-x-2 cursor-pointer"
//           onClick={onGoHome}
//         >
//           <Image
//             src="/logo.png"
//             alt="Logo"
//             width={150}
//             height={40}
//             className="ml-24 mt-5"
//           />
//         </div>
//         {/* ADDED: Logout button for the Landing Page */}
//         <div className="flex items-center space-x-4">
//           <button
//             onClick={onLogout}
//             className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2"
//           >
//             <LogOut className="w-4 h-4" />
//             <span>Logout</span>
//           </button>
//         </div>
//       </header>
//       {/* Hero Section */}
//       <div className="max-w-4xl mx-auto px-6 py-16 text-center">
//         <h1 className="text-5xl font-bold text-gray-900 mb-6 leading-tight">
//           Explore US Election Laws with SonlineAI Assistant
//         </h1>
//         <p className="text-xl text-gray-600 mb-12 max-w-2xl mx-auto">
//           Federal, State & County-Level Election Law Guidance with SonlineAI Support
//         </p>
//         {/* Cards Section */}
//         <div className="grid md:grid-cols-3 gap-8 max-w-3xl mx-auto">
//           {/* Federal Law Card */}
//           <div
//             onClick={() => onNavigate('federal')}
//             className="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 cursor-pointer border-2 border-transparent hover:border-blue-200 transform hover:-translate-y-2"
//           >
//             <div className="w-16 h-16 bg-blue-600 rounded-xl flex items-center justify-center mx-auto mb-6 group-hover:bg-blue-700 transition-colors">
//               <FileText className="w-8 h-8 text-white" />
//             </div>
//             <h3 className="text-2xl font-bold text-gray-900 mb-4">Federal Law</h3>
//             <p className="text-gray-600 leading-relaxed">
//               Access federal election statutes, FEC regulations, and constitutional provisions governing elections nationwide.
//             </p>
//             <div className="mt-6 text-blue-600 font-medium group-hover:text-blue-700">
//               Explore Federal Laws →
//             </div>
//           </div>
//           {/* State Law Card */}
//           <div
//             onClick={() => onNavigate('state')}
//             className="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 cursor-pointer border-2 border-transparent hover:border-green-200 transform hover:-translate-y-2"
//           >
//             <div className="w-16 h-16 bg-green-600 rounded-xl flex items-center justify-center mx-auto mb-6 group-hover:bg-green-700 transition-colors">
//               <FileText className="w-8 h-8 text-white" />
//             </div>
//             <h3 className="text-2xl font-bold text-gray-900 mb-4">State Law</h3>
//             <p className="text-gray-600 leading-relaxed">
//               Navigate state-specific election codes, voter registration requirements, and campaign finance laws.
//             </p>
//             <div className="mt-6 text-green-600 font-medium group-hover:text-green-700">
//               Explore State Laws →
//             </div>
//           </div>
//           {/* County Law Card */}
//           <div
//             onClick={() => onNavigate('county')}
//             className="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 cursor-pointer border-2 border-transparent hover:border-purple-200 transform hover:-translate-y-2"
//           >
//             <div className="w-16 h-16 bg-purple-600 rounded-xl flex items-center justify-center mx-auto mb-6 group-hover:bg-purple-700 transition-colors">
//               <FileText className="w-8 h-8 text-white" />
//             </div>
//             <h3 className="text-2xl font-bold text-gray-900 mb-4">County Law</h3>
//             <p className="text-gray-600 leading-relaxed">
//               Understand local election procedures, polling guidelines, and county-specific administrative rules.
//             </p>
//             <div className="mt-6 text-purple-600 font-medium group-hover:text-purple-700">
//               Explore County Laws →
//             </div>
//           </div>
//         </div>
//         {/* Features Section */}
//         <div className="mt-20 bg-white rounded-2xl p-8 shadow-lg max-w-2xl mx-auto">
//           <h3 className="text-2xl font-bold text-gray-900 mb-6">AI-Powered Legal Research</h3>
//           <div className="grid md:grid-cols-2 gap-6 text-left">
//             <div className="flex items-start space-x-3">
//               <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
//                 <Search className="w-3 h-3 text-blue-600" />
//               </div>
//               <div>
//                 <h4 className="font-semibold text-gray-900">Smart Search</h4>
//                 <p className="text-gray-600 text-sm">Find relevant laws instantly with natural language queries</p>
//               </div>
//             </div>
//             <div className="flex items-start space-x-3">
//               <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
//                 <MessageCircle className="w-3 h-3 text-green-600" />
//               </div>
//               <div>
//                 <h4 className="font-semibold text-gray-900">Interactive Chat</h4>
//                 <p className="text-gray-600 text-sm">Get explanations and guidance through conversation</p>
//               </div>
//             </div>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };
// const StateSelectionPage: React.FC<StateSelectionPageProps> = ({ onStateSelect, onBack, onGoHome, onLogout }) => {
//   return (
//     <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-green-50">
//       {/* Header */}
//       <header className="flex justify-between items-center p-6">
//         <div
//           className="flex items-center space-x-2 cursor-pointer"
//           onClick={onGoHome}
//         >
//           <Image
//             src="/logo.png"
//             alt="Logo"
//             width={150}
//             height={40}
//             className="ml-24 mt-5"
//           />
//         </div>
//         <div className="flex items-center space-x-4">
//           <button
//             onClick={onBack}
//             className="text-gray-600 hover:text-gray-900 font-medium flex items-center space-x-1"
//           >
//             <ArrowLeft className="w-4 h-4" />
//             <span>Back to Home</span>
//           </button>
//           <button
//             onClick={onLogout}
//             className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2"
//           >
//             <LogOut className="w-4 h-4" />
//             <span>Logout</span>
//           </button>
//         </div>
//       </header>
//       {/* Content */}
//       <div className="max-w-6xl mx-auto px-6 py-16">
//         <div className="text-center mb-12">
//           <h1 className="text-4xl font-bold text-gray-900 mb-4">
//             Select Your State
//           </h1>
//           <p className="text-xl text-gray-600 mb-6">
//             Choose a state to explore its specific election laws and regulations
//           </p>
          
//           {/* Legend */}
//           <div className="flex justify-center items-center space-x-6 mb-8">
//             <div className="flex items-center space-x-2">
//               <div className="w-4 h-4 bg-gradient-to-r from-green-500 to-green-600 rounded shadow-sm"></div>
//               <span className="text-sm font-medium text-gray-700">Available: Georgia</span>
//             </div>
//             <div className="flex items-center space-x-2">
//               <div className="w-4 h-4 bg-gray-300 rounded opacity-60"></div>
//               <span className="text-sm text-gray-500">Coming Soon</span>
//             </div>
//           </div>
//         </div>
//         {/* States Grid */}
//         <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-4">
//           {US_STATES.map((state) => {
//             const isAvailable = availableStates.includes(state);
//             return (
//               <button
//                 key={state}
//                 onClick={() => isAvailable && onStateSelect(state)}
//                 className={`p-4 rounded-xl text-center transition-all duration-200 text-sm font-medium ${
//                   isAvailable
//                     ? 'bg-gradient-to-r from-green-500 to-green-600 text-white shadow-lg hover:shadow-xl hover:scale-105 cursor-pointer border-2 border-green-400'
//                     : 'bg-white border-2 border-gray-200 text-gray-500 cursor-not-allowed opacity-60 hover:opacity-70'
//                 }`}
//                 disabled={!isAvailable}
//               >
//                 <div className="font-semibold">{state}</div>
//                 {!isAvailable && (
//                   <div className="text-xs mt-1 text-gray-400">Coming Soon</div>
//                 )}
//                 {isAvailable && (
//                   <div className="text-xs mt-1 text-green-100">Available Now</div>
//                 )}
//               </button>
//             );
//           })}
//         </div>
//         {/* Available State Highlight */}
//         <div className="mt-16 text-center">
//           <div className="bg-white rounded-2xl p-8 shadow-lg max-w-2xl mx-auto border-l-4 border-green-500">
//             <h3 className="text-2xl font-bold text-gray-900 mb-4"> Georgia Election Laws Ready!</h3>
//             <p className="text-gray-600 mb-6">
//               Comprehensive coverage of Georgia state election laws with 4 specialized document categories
//               and AI-powered analysis with source citations.
//             </p>
//             <button
//               onClick={() => onStateSelect('Georgia')}
//               className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-8 py-3 rounded-lg font-semibold transition-all duration-200 shadow-lg hover:shadow-xl"
//             >
//               Explore Georgia Laws →
//             </button>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };
// const GeorgiaDocumentSelection: React.FC<GeorgiaDocumentSelectionProps> = ({
//   onDocumentSelect,
//   onBack,
//   onGoHome,
//   onLogout,
// }) => {
//   return (
//     <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-green-50">
//       {/* Header */}
//       <header className="flex justify-between items-center p-6">
//         <div
//           className="flex items-center space-x-2 cursor-pointer"
//           onClick={onGoHome}
//         >
//           <Image
//             src="/logo.png"
//             alt="Logo"
//             width={150}
//             height={40}
//             className="ml-24 mt-5"
//           />
//         </div>
//         <div className="flex items-center space-x-4">
//           <button
//             onClick={onBack}
//             className="text-gray-600 hover:text-gray-900 font-medium flex items-center space-x-1"
//           >
//             <ArrowLeft className="w-4 h-4" />
//             <span>Back to States</span>
//           </button>
//           <button
//             onClick={onLogout}
//             className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2"
//           >
//             <LogOut className="w-4 h-4" />
//             <span>Logout</span>
//           </button>
//         </div>
//       </header>
//       {/* Content */}
//       <div className="max-w-7xl mx-auto px-6 py-16">
//         <div className="text-center mb-12">
//           <div className="flex items-center justify-center gap-3 mb-4">
//             <span className="text-4xl"></span>
//             <h1 className="text-4xl font-bold text-gray-900">
//               Georgia Election Laws
//             </h1>
//           </div>
//           <p className="text-xl text-gray-600 mb-6">
//             Select a document category to explore specific Georgia election law provisions
//           </p>
          
//           {/* Info Badge */}
//           <div className="inline-flex items-center gap-2 bg-green-100 text-green-800 px-4 py-2 rounded-full text-sm font-medium">
//             <span className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
//             AI-Powered Analysis with Source Citations
//           </div>
//         </div>
//         {/* Document Selection Grid */}
//         <div className="grid md:grid-cols-2 gap-8 max-w-6xl mx-auto">
//           {georgiaDocuments.map((doc) => (
//             <div
//               key={doc.id}
//               onClick={() => onDocumentSelect(doc)}
//               className="group bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 cursor-pointer border-2 border-transparent hover:border-gray-200 transform hover:-translate-y-3"
//             >
//               {/* Header */}
//               <div className="flex items-center gap-4 mb-6">
//                 <div className={`w-16 h-16 bg-gradient-to-r ${doc.color} rounded-2xl flex items-center justify-center text-2xl shadow-lg group-hover:scale-110 transition-transform`}>
//                   {doc.icon}
//                 </div>
//                 <div className="flex-1">
//                   <h3 className="text-2xl font-bold text-gray-900 mb-2 group-hover:text-gray-700 transition-colors">
//                     {doc.title}
//                   </h3>
//                   <div className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold border ${doc.lightColor}`}>
//                     📄 {doc.filename}
//                   </div>
//                 </div>
//               </div>
//               {/* Description */}
//               <p className="text-gray-600 leading-relaxed mb-6 text-base">
//                 {doc.description}
//               </p>
//               {/* Features */}
//               <div className="space-y-3 mb-6">
//                 <div className="flex items-center gap-3 text-sm text-gray-500">
//                   <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center">
//                     <span className="w-2 h-2 bg-green-500 rounded-full"></span>
//                   </div>
//                   <span>Real document analysis</span>
//                 </div>
//                 <div className="flex items-center gap-3 text-sm text-gray-500">
//                   <div className="w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center">
//                     <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
//                   </div>
//                   <span>Source citations included</span>
//                 </div>
//                 <div className="flex items-center gap-3 text-sm text-gray-500">
//                   <div className="w-5 h-5 bg-purple-100 rounded-full flex items-center justify-center">
//                     <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
//                   </div>
//                   <span>Conversational AI assistance</span>
//                 </div>
//               </div>
//               {/* Action Button */}
//               <div className={`w-full bg-gradient-to-r ${doc.color} text-white py-4 rounded-2xl text-center font-semibold group-hover:shadow-lg transition-all duration-200 flex items-center justify-center gap-2`}>
//                 <FileText className="w-5 h-5" />
//                 <span>Explore This Document →</span>
//               </div>
//             </div>
//           ))}
//         </div>
//         {/* Bottom Info Section */}
//         <div className="mt-16 text-center">
//           <div className="bg-white rounded-3xl p-8 shadow-lg max-w-3xl mx-auto border-l-4 border-green-500">
//             <h3 className="text-2xl font-bold text-gray-900 mb-4">
//               📚 Document-Based Legal Analysis
//             </h3>
//             <div className="grid md:grid-cols-3 gap-6 text-left">
//               <div className="flex flex-col items-center text-center">
//                 <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mb-3">
//                   <Search className="w-6 h-6 text-green-600" />
//                 </div>
//                 <h4 className="font-semibold text-gray-900 mb-2">Smart Analysis</h4>
//                 <p className="text-gray-600 text-sm">AI analyzes actual legal document content to provide accurate answers</p>
//               </div>
//               <div className="flex flex-col items-center text-center">
//                 <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mb-3">
//                   <FileText className="w-6 h-6 text-blue-600" />
//                 </div>
//                 <h4 className="font-semibold text-gray-900 mb-2">Source Citations</h4>
//                 <p className="text-gray-600 text-sm">Every answer includes specific references to the source document</p>
//               </div>
//               <div className="flex flex-col items-center text-center">
//                 <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mb-3">
//                   <MessageCircle className="w-6 h-6 text-purple-600" />
//                 </div>
//                 <h4 className="font-semibold text-gray-900 mb-2">Conversational</h4>
//                 <p className="text-gray-600 text-sm">Natural conversation flow with memory of previous questions</p>
//               </div>
//             </div>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };
// export default function Home() {
//   const [currentPage, setCurrentPage] = useState<CurrentPage>('login'); // Initial state is now 'login'
//   const [lawType, setLawType] = useState<LawType | null>(null);
//   const [isLoggedIn, setIsLoggedIn] = useState(false); // New state to track login status
//   const [selectedGeorgiaDocument, setSelectedGeorgiaDocument] = useState<GeorgiaDocument | null>(null);
//   const [selectedGeorgiaArticle, setSelectedGeorgiaArticle] = useState<GeorgiaArticle | null>(null);

//   // New function to handle successful login from the LoginPage
//   const handleLoginSuccess = () => {
//     setIsLoggedIn(true);
//     setCurrentPage('landing'); // After login, navigate to the landing page
//   };

//   // This function now handles navigation directly, as the user is already logged in
//   const handleNavigate = (type: LawType): void => {
//     if (type === 'state') {
//       setCurrentPage('stateSelection');
//     } else {
//       setLawType(type);
//       setCurrentPage('chat');
//     }
//   };

//   const handleStateSelect = (stateName: string): void => {
//     if (stateName === 'Georgia') {
//       setCurrentPage('georgiaDocuments');
//     } else {
//       setLawType('state');
//       setCurrentPage('chat');
//     }
//   };
//   const handleGeorgiaDocumentSelect = (document: GeorgiaDocument): void => {
//     if (document.id === 2) {
//       setSelectedGeorgiaDocument(document);
//       setCurrentPage('georgiaArticleSelection');
//     } else {
//       setSelectedGeorgiaDocument(document);
//       setLawType('state');
//       setCurrentPage('chat');
//     }
//   };

//   const handleGeorgiaArticleSelect = (article: GeorgiaArticle): void => {
//     setSelectedGeorgiaArticle(article);
//     setLawType('state');
//     setCurrentPage('chat');
//   };

//   const handleBackFromArticleSelection = (): void => {
//     setCurrentPage('georgiaDocuments');
//     setSelectedGeorgiaArticle(null);
//   };
  
//   // This function handles navigation back to the home page (landing)
//   const handleGoHome = (): void => {
//     setCurrentPage('landing');
//   };

//   // This function handles logout and returns to the login page
//   const handleLogout = (): void => {
//     setIsLoggedIn(false);
//     setCurrentPage('login'); // Return to login page on logout
//     setLawType(null);
//     setSelectedGeorgiaDocument(null);
//     setSelectedGeorgiaArticle(null);
//   };

//   const handleBack = (): void => {
//     if (currentPage === 'stateSelection') {
//       setCurrentPage('landing');
//     } else if (currentPage === 'georgiaDocuments') {
//       setCurrentPage('stateSelection');
//     } else if (currentPage === 'georgiaArticleSelection') {
//       setCurrentPage('georgiaDocuments');
//       setSelectedGeorgiaArticle(null);
//     } else {
//       // From chat, determine where to go back
//       if (selectedGeorgiaArticle) {
//         setCurrentPage('georgiaArticleSelection');
//         setSelectedGeorgiaArticle(null);
//       } else if (selectedGeorgiaDocument) {
//         setCurrentPage('georgiaDocuments');
//         setSelectedGeorgiaDocument(null);
//       } else {
//         setCurrentPage('landing');
//         setLawType(null);
//       }
//     }
//   };

//   const enhancedDocumentData = {
//     ...documentData,
//     state: selectedGeorgiaDocument ? [{
//       id: selectedGeorgiaDocument.id,
//       title: selectedGeorgiaDocument.title,
//       type: "Georgia State Law",
//       filename: selectedGeorgiaDocument.filename
//     }] : []
//   };

//   // New conditional rendering logic
//   if (!isLoggedIn) {
//     // If not logged in, show the login page
//     return <LoginPage onLoginSuccess={handleLoginSuccess} />;
//   }

//   // If logged in, show the appropriate page based on the currentPage state
//   if (currentPage === 'landing') {
//     // UPDATED: Now passing onLogout to the LandingPage
//     return <LandingPage onNavigate={handleNavigate} onGoHome={handleGoHome} onLogout={handleLogout} />;
//   }

//   if (currentPage === 'stateSelection') {
//     return <StateSelectionPage onStateSelect={handleStateSelect} onBack={handleBack} onGoHome={handleGoHome} onLogout={handleLogout} />;
//   }

//   if (currentPage === 'georgiaDocuments') {
//     return (
//       <GeorgiaDocumentSelection
//         onDocumentSelect={handleGeorgiaDocumentSelect}
//         onBack={handleBack}
//         onGoHome={handleGoHome}
//         onLogout={handleLogout}
//       />
//     );
//   }

//   if (currentPage === 'georgiaArticleSelection') {
//     return (
//       <GeorgiaArticleSelector
//         onBack={handleBackFromArticleSelection}
//         onSelectArticle={handleGeorgiaArticleSelect}
//         onGoHome={handleGoHome}
//         onLogout={handleLogout}
//       />
//     );
//   }

//   if (currentPage === 'chat' && lawType) {
//     return (
//       <ChatInterface
//         lawType={lawType}
//         onBack={handleBack}
//         documentData={enhancedDocumentData}
//         selectedGeorgiaDocument={selectedGeorgiaDocument}
//         selectedGeorgiaArticle={selectedGeorgiaArticle}
//         onGoHome={handleGoHome}
//         onLogout={handleLogout}
//       />
//     );
//   }

//   return <div>Loading...</div>;
// }


'use client';

import React, { useState } from 'react';
import { Search, FileText, MessageCircle, Send, User, LogOut, ArrowLeft, Menu, X, Gavel, Handshake, Briefcase, DollarSign, Globe, Book, Users, ClipboardList, TrendingUp } from 'lucide-react';
import Image from 'next/image';
import ChatInterface from '../components/ChatInterface';
import GeorgiaArticleSelector from '../components/GeorgiaArticleSelector';
import LoginPage from '../components/LoginPage';
import FederalLawSelector from '../components/FederalLawSelector'; // Import the new component

// Type definitions
interface Document {
  id: number;
  title: string;
  type: string;
  filename?: string;
}
interface GeorgiaDocument {
  id: number;
  title: string;
  description: string;
  filename: string;
  icon: string;
  color: string;
  lightColor: string;
}

// Add the GeorgiaArticle interface for the article selector
interface GeorgiaArticle {
  id: number;
  title: string;
  subtitle: string;
  filename: string;
  sections: string;
  icon: React.ReactNode;
  color: string;
  lightColor: string;
  description: string;
}

// ADDED: Interface for Federal Title
interface FederalTitle {
  id: number;
  title: string;
  subtitle: string;
  filename: string;
  sections: string;
  icon: React.ReactNode;
  color: string;
  lightColor: string;
  description: string;
}

interface Message {
  id: number;
  type: 'user' | 'ai';
  content: string;
  rating?: 'up' | 'down' | null;
}
type LawType = 'federal' | 'state' | 'county';

// UPDATED: Added 'federalLawSelection' to the CurrentPage type
type CurrentPage = 'landing' | 'stateSelection' | 'georgiaDocuments' | 'georgiaArticleSelection' | 'federalLawSelection' | 'chat' | 'login';
interface DocumentData {
  [key: string]: Document[];
}
interface LandingPageProps {
  onNavigate: (type: LawType) => void;
  onGoHome: () => void;
  onLogout: () => void;
}
interface StateSelectionPageProps {
  onStateSelect: (stateName: string) => void;
  onBack: () => void;
  onGoHome: () => void;
  onLogout: () => void;
}
interface GeorgiaDocumentSelectionProps {
  onDocumentSelect: (document: GeorgiaDocument) => void;
  onBack: () => void;
  onGoHome: () => void;
  onLogout: () => void;
}

// Mock document data for federal and county
const documentData: DocumentData = {
  federal: [
    { id: 1, title: "Federal Election Campaign Act", type: "Federal Statute" },
    { id: 2, title: "Voting Rights Act of 1965", type: "Federal Statute" },
    { id: 3, title: "Help America Vote Act", type: "Federal Statute" },
    { id: 4, title: "FEC Regulations 11 CFR", type: "Federal Regulation" }
  ],
  county: [
    { id: 9, title: "GA County Election Procedures", type: "County Ordinance" },
    { id: 10, title: "GA County Polling Guidelines", type: "County Policy" },
    { id: 11, title: "GA County Ballot Design", type: "County Standard" },
    { id: 12, title: "GA County Voter Outreach", type: "County Policy" }
  ]
};

// Georgia-specific documents
const georgiaDocuments: GeorgiaDocument[] = [
  {
    id: 1,
    title: "Congressional Districts",
    description: "Laws governing the establishment, modification, and administration of congressional district boundaries in Georgia.",
    filename: "1.txt",
    icon: "🗺️",
    color: "from-blue-500 to-blue-600",
    lightColor: "bg-blue-50 text-blue-800 border-blue-200"
  },
  {
    id: 2,
    title: "Elections & Election Systems",
    description: "Comprehensive election procedures, voting systems, ballot requirements, and election administration in Georgia.",
    filename: "2.txt",
    icon: "🗳️",
    color: "from-green-500 to-green-600",
    lightColor: "bg-green-50 text-green-800 border-green-200"
  },
  {
    id: 3,
    title: "Recall of Public Officers",
    description: "Legal procedures and requirements for the recall of elected officials and public officers in Georgia.",
    filename: "3.txt",
    icon: "⚖️",
    color: "from-purple-500 to-purple-600",
    lightColor: "bg-purple-50 text-purple-800 border-purple-200"
  },
  {
    id: 4,
    title: "Gov Transparency & Campaign Finance",
    description: "Government transparency requirements, campaign finance regulations, and disclosure laws in Georgia.",
    filename: "4.txt",
    icon: "💰",
    color: "from-orange-500 to-orange-600",
    lightColor: "bg-orange-50 text-orange-800 border-orange-200"
  }
];

// US States data
const US_STATES = [
  'Alabama', 'Alaska', 'Arizona', 'Arkansas', 'California', 'Colorado',
  'Connecticut', 'Delaware', 'Florida', 'Georgia', 'Hawaii', 'Idaho',
  'Illinois', 'Indiana', 'Iowa', 'Kansas', 'Kentucky', 'Louisiana',
  'Maine', 'Maryland', 'Massachusetts', 'Michigan', 'Minnesota',
  'Mississippi', 'Missouri', 'Montana', 'Nebraska', 'Nevada',
  'New Hampshire', 'New Jersey', 'New Mexico', 'New York',
  'North Carolina', 'North Dakota', 'Ohio', 'Oklahoma', 'Oregon',
  'Pennsylvania', 'Rhode Island', 'South Carolina', 'South Dakota',
  'Tennessee', 'Texas', 'Utah', 'Vermont', 'Virginia', 'Washington',
  'West Virginia', 'Wisconsin', 'Wyoming'
];

const availableStates = ['Georgia'];

const LandingPage: React.FC<LandingPageProps> = ({ onNavigate, onGoHome, onLogout }) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-red-50">
      {/* Header */}
      <header className="flex justify-between items-center p-6">
        <div
          className="flex items-center space-x-2 cursor-pointer"
          onClick={onGoHome}
        >
          <Image
            src="/logo.png"
            alt="Logo"
            width={150}
            height={40}
            className="ml-24 mt-5"
          />
        </div>
        <div className="flex items-center space-x-4">
          <button
            onClick={onLogout}
            className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2"
          >
            <LogOut className="w-4 h-4" />
            <span>Logout</span>
          </button>
        </div>
      </header>
      {/* Hero Section */}
      <div className="max-w-4xl mx-auto px-6 py-16 text-center">
        <h1 className="text-5xl font-bold text-gray-900 mb-6 leading-tight">
          Explore US Election Laws with SonlineAI Assistant
        </h1>
        <p className="text-xl text-gray-600 mb-12 max-w-2xl mx-auto">
          Federal, State & County-Level Election Law Guidance with SonlineAI Support
        </p>
        {/* Cards Section */}
        <div className="grid md:grid-cols-3 gap-8 max-w-3xl mx-auto">
          {/* Federal Law Card */}
          <div
            onClick={() => onNavigate('federal')}
            className="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 cursor-pointer border-2 border-transparent hover:border-blue-200 transform hover:-translate-y-2"
          >
            <div className="w-16 h-16 bg-blue-600 rounded-xl flex items-center justify-center mx-auto mb-6 group-hover:bg-blue-700 transition-colors">
              <FileText className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Federal Law</h3>
            <p className="text-gray-600 leading-relaxed">
              Access federal election statutes, FEC regulations, and constitutional provisions governing elections nationwide.
            </p>
            <div className="mt-6 text-blue-600 font-medium group-hover:text-blue-700">
              Explore Federal Laws →
            </div>
          </div>
          {/* State Law Card */}
          <div
            onClick={() => onNavigate('state')}
            className="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 cursor-pointer border-2 border-transparent hover:border-green-200 transform hover:-translate-y-2"
          >
            <div className="w-16 h-16 bg-green-600 rounded-xl flex items-center justify-center mx-auto mb-6 group-hover:bg-green-700 transition-colors">
              <FileText className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">State Law</h3>
            <p className="text-gray-600 leading-relaxed">
              Navigate state-specific election codes, voter registration requirements, and campaign finance laws.
            </p>
            <div className="mt-6 text-green-600 font-medium group-hover:text-green-700">
              Explore State Laws →
            </div>
          </div>
          {/* County Law Card */}
          <div
            onClick={() => onNavigate('county')}
            className="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 cursor-pointer border-2 border-transparent hover:border-purple-200 transform hover:-translate-y-2"
          >
            <div className="w-16 h-16 bg-purple-600 rounded-xl flex items-center justify-center mx-auto mb-6 group-hover:bg-purple-700 transition-colors">
              <FileText className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">County Law</h3>
            <p className="text-gray-600 leading-relaxed">
              Understand local election procedures, polling guidelines, and county-specific administrative rules.
            </p>
            <div className="mt-6 text-purple-600 font-medium group-hover:text-purple-700">
              Explore County Laws →
            </div>
          </div>
        </div>
        {/* Features Section */}
        <div className="mt-20 bg-white rounded-2xl p-8 shadow-lg max-w-2xl mx-auto">
          <h3 className="text-2xl font-bold text-gray-900 mb-6">AI-Powered Legal Research</h3>
          <div className="grid md:grid-cols-2 gap-6 text-left">
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                <Search className="w-3 h-3 text-blue-600" />
              </div>
              <div>
                <h4 className="font-semibold text-gray-900">Smart Search</h4>
                <p className="text-gray-600 text-sm">Find relevant laws instantly with natural language queries</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                <MessageCircle className="w-3 h-3 text-green-600" />
              </div>
              <div>
                <h4 className="font-semibold text-gray-900">Interactive Chat</h4>
                <p className="text-gray-600 text-sm">Get explanations and guidance through conversation</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
const StateSelectionPage: React.FC<StateSelectionPageProps> = ({ onStateSelect, onBack, onGoHome, onLogout }) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-green-50">
      {/* Header */}
      <header className="flex justify-between items-center p-6">
        <div
          className="flex items-center space-x-2 cursor-pointer"
          onClick={onGoHome}
        >
          <Image
            src="/logo.png"
            alt="Logo"
            width={150}
            height={40}
            className="ml-24 mt-5"
          />
        </div>
        <div className="flex items-center space-x-4">
          <button
            onClick={onBack}
            className="text-gray-600 hover:text-gray-900 font-medium flex items-center space-x-1"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Home</span>
          </button>
          <button
            onClick={onLogout}
            className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2"
          >
            <LogOut className="w-4 h-4" />
            <span>Logout</span>
          </button>
        </div>
      </header>
      {/* Content */}
      <div className="max-w-6xl mx-auto px-6 py-16">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Select Your State
          </h1>
          <p className="text-xl text-gray-600 mb-6">
            Choose a state to explore its specific election laws and regulations
          </p>

          {/* Legend */}
          <div className="flex justify-center items-center space-x-6 mb-8">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-gradient-to-r from-green-500 to-green-600 rounded shadow-sm"></div>
              <span className="text-sm font-medium text-gray-700">Available: Georgia</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-gray-300 rounded opacity-60"></div>
              <span className="text-sm text-gray-500">Coming Soon</span>
            </div>
          </div>
        </div>
        {/* States Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-4">
          {US_STATES.map((state) => {
            const isAvailable = availableStates.includes(state);
            return (
              <button
                key={state}
                onClick={() => isAvailable && onStateSelect(state)}
                className={`p-4 rounded-xl text-center transition-all duration-200 text-sm font-medium ${
                  isAvailable
                    ? 'bg-gradient-to-r from-green-500 to-green-600 text-white shadow-lg hover:shadow-xl hover:scale-105 cursor-pointer border-2 border-green-400'
                    : 'bg-white border-2 border-gray-200 text-gray-500 cursor-not-allowed opacity-60 hover:opacity-70'
                }`}
                disabled={!isAvailable}
              >
                <div className="font-semibold">{state}</div>
                {!isAvailable && (
                  <div className="text-xs mt-1 text-gray-400">Coming Soon</div>
                )}
                {isAvailable && (
                  <div className="text-xs mt-1 text-green-100">Available Now</div>
                )}
              </button>
            );
          })}
        </div>
        {/* Available State Highlight */}
        <div className="mt-16 text-center">
          <div className="bg-white rounded-2xl p-8 shadow-lg max-w-2xl mx-auto border-l-4 border-green-500">
            <h3 className="text-2xl font-bold text-gray-900 mb-4"> Georgia Election Laws Ready!</h3>
            <p className="text-gray-600 mb-6">
              Comprehensive coverage of Georgia state election laws with 4 specialized document categories
              and AI-powered analysis with source citations.
            </p>
            <button
              onClick={() => onStateSelect('Georgia')}
              className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-8 py-3 rounded-lg font-semibold transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              Explore Georgia Laws →
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
const GeorgiaDocumentSelection: React.FC<GeorgiaDocumentSelectionProps> = ({
  onDocumentSelect,
  onBack,
  onGoHome,
  onLogout,
}) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-green-50">
      {/* Header */}
      <header className="flex justify-between items-center p-6">
        <div
          className="flex items-center space-x-2 cursor-pointer"
          onClick={onGoHome}
        >
          <Image
            src="/logo.png"
            alt="Logo"
            width={150}
            height={40}
            className="ml-24 mt-5"
          />
        </div>
        <div className="flex items-center space-x-4">
          <button
            onClick={onBack}
            className="text-gray-600 hover:text-gray-900 font-medium flex items-center space-x-1"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to States</span>
          </button>
          <button
            onClick={onLogout}
            className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2"
          >
            <LogOut className="w-4 h-4" />
            <span>Logout</span>
          </button>
        </div>
      </header>
      {/* Content */}
      <div className="max-w-7xl mx-auto px-6 py-16">
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-3 mb-4">
            <span className="text-4xl"></span>
            <h1 className="text-4xl font-bold text-gray-900">
              Georgia Election Laws
            </h1>
          </div>
          <p className="text-xl text-gray-600 mb-6">
            Select a document category to explore specific Georgia election law provisions
          </p>

          {/* Info Badge */}
          <div className="inline-flex items-center gap-2 bg-green-100 text-green-800 px-4 py-2 rounded-full text-sm font-medium">
            <span className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
            AI-Powered Analysis with Source Citations
          </div>
        </div>
        {/* Document Selection Grid */}
        <div className="grid md:grid-cols-2 gap-8 max-w-6xl mx-auto">
          {georgiaDocuments.map((doc) => (
            <div
              key={doc.id}
              onClick={() => onDocumentSelect(doc)}
              className="group bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 cursor-pointer border-2 border-transparent hover:border-gray-200 transform hover:-translate-y-3"
            >
              {/* Header */}
              <div className="flex items-center gap-4 mb-6">
                <div className={`w-16 h-16 bg-gradient-to-r ${doc.color} rounded-2xl flex items-center justify-center text-2xl shadow-lg group-hover:scale-110 transition-transform`}>
                  {doc.icon}
                </div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2 group-hover:text-gray-700 transition-colors">
                    {doc.title}
                  </h3>
                  <div className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold border ${doc.lightColor}`}>
                    📄 {doc.filename}
                  </div>
                </div>
              </div>
              {/* Description */}
              <p className="text-gray-600 leading-relaxed mb-6 text-base">
                {doc.description}
              </p>
              {/* Features */}
              <div className="space-y-3 mb-6">
                <div className="flex items-center gap-3 text-sm text-gray-500">
                  <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center">
                    <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  </div>
                  <span>Real document analysis</span>
                </div>
                <div className="flex items-center gap-3 text-sm text-gray-500">
                  <div className="w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                  </div>
                  <span>Source citations included</span>
                </div>
                <div className="flex items-center gap-3 text-sm text-gray-500">
                  <div className="w-5 h-5 bg-purple-100 rounded-full flex items-center justify-center">
                    <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
                  </div>
                  <span>Conversational AI assistance</span>
                </div>
              </div>
              {/* Action Button */}
              <div className={`w-full bg-gradient-to-r ${doc.color} text-white py-4 rounded-2xl text-center font-semibold group-hover:shadow-lg transition-all duration-200 flex items-center justify-center gap-2`}>
                <FileText className="w-5 h-5" />
                <span>Explore This Document →</span>
              </div>
            </div>
          ))}
        </div>
        {/* Bottom Info Section */}
        <div className="mt-16 text-center">
          <div className="bg-white rounded-3xl p-8 shadow-lg max-w-3xl mx-auto border-l-4 border-green-500">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              📚 Document-Based Legal Analysis
            </h3>
            <div className="grid md:grid-cols-3 gap-6 text-left">
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mb-3">
                  <Search className="w-6 h-6 text-green-600" />
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">Smart Analysis</h4>
                <p className="text-gray-600 text-sm">AI analyzes actual legal document content to provide accurate answers</p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mb-3">
                  <FileText className="w-6 h-6 text-blue-600" />
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">Source Citations</h4>
                <p className="text-gray-600 text-sm">Every answer includes specific references to the source document</p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mb-3">
                  <MessageCircle className="w-6 h-6 text-purple-600" />
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">Conversational</h4>
                <p className="text-gray-600 text-sm">Natural conversation flow with memory of previous questions</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default function Home() {
  const [currentPage, setCurrentPage] = useState<CurrentPage>('login');
  const [lawType, setLawType] = useState<LawType | null>(null);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [selectedGeorgiaDocument, setSelectedGeorgiaDocument] = useState<GeorgiaDocument | null>(null);
  const [selectedGeorgiaArticle, setSelectedGeorgiaArticle] = useState<GeorgiaArticle | null>(null);
  const [selectedFederalTitle, setSelectedFederalTitle] = useState<FederalTitle | null>(null); // ADDED: New state for federal titles

  const handleLoginSuccess = () => {
    setIsLoggedIn(true);
    setCurrentPage('landing');
  };

  const handleNavigate = (type: LawType): void => {
    if (type === 'state') {
      setCurrentPage('stateSelection');
    } else if (type === 'federal') { // UPDATED: Redirect to new federal selection page
      setCurrentPage('federalLawSelection');
    } else {
      setLawType(type);
      setCurrentPage('chat');
    }
  };

  const handleStateSelect = (stateName: string): void => {
    if (stateName === 'Georgia') {
      setCurrentPage('georgiaDocuments');
    } else {
      setLawType('state');
      setCurrentPage('chat');
    }
  };

  const handleGeorgiaDocumentSelect = (document: GeorgiaDocument): void => {
    if (document.id === 2) {
      setSelectedGeorgiaDocument(document);
      setCurrentPage('georgiaArticleSelection');
    } else {
      setSelectedGeorgiaDocument(document);
      setLawType('state');
      setCurrentPage('chat');
    }
  };

  const handleGeorgiaArticleSelect = (article: GeorgiaArticle): void => {
    setSelectedGeorgiaArticle(article);
    setLawType('state');
    setCurrentPage('chat');
  };

  const handleFederalTitleSelect = (title: FederalTitle): void => { // ADDED: New handler for federal titles
    setSelectedFederalTitle(title);
    setLawType('federal');
    setCurrentPage('chat');
  };

  const handleBackFromArticleSelection = (): void => {
    setCurrentPage('georgiaDocuments');
    setSelectedGeorgiaArticle(null);
  };

  const handleGoHome = (): void => {
    setCurrentPage('landing');
  };

  const handleLogout = (): void => {
    setIsLoggedIn(false);
    setCurrentPage('login');
    setLawType(null);
    setSelectedGeorgiaDocument(null);
    setSelectedGeorgiaArticle(null);
    setSelectedFederalTitle(null); // ADDED: Reset federal title on logout
  };

  const handleBack = (): void => {
    if (currentPage === 'stateSelection') {
      setCurrentPage('landing');
    } else if (currentPage === 'georgiaDocuments') {
      setCurrentPage('stateSelection');
    } else if (currentPage === 'georgiaArticleSelection') {
      setCurrentPage('georgiaDocuments');
      setSelectedGeorgiaArticle(null);
    } else if (currentPage === 'federalLawSelection') { // ADDED: Back from federal selection page
      setCurrentPage('landing');
      setSelectedFederalTitle(null);
    } else {
      // From chat, determine where to go back
      if (selectedGeorgiaArticle) {
        setCurrentPage('georgiaArticleSelection');
        setSelectedGeorgiaArticle(null);
      } else if (selectedGeorgiaDocument) {
        setCurrentPage('georgiaDocuments');
        setSelectedGeorgiaDocument(null);
      } else if (selectedFederalTitle) { // ADDED: Back from chat to federal selection
        setCurrentPage('federalLawSelection');
        setSelectedFederalTitle(null);
      } else {
        setCurrentPage('landing');
        setLawType(null);
      }
    }
  };

  const enhancedDocumentData = {
    ...documentData,
    state: selectedGeorgiaDocument ? [{
      id: selectedGeorgiaDocument.id,
      title: selectedGeorgiaDocument.title,
      type: "Georgia State Law",
      filename: selectedGeorgiaDocument.filename
    }] : [],
    federal: selectedFederalTitle ? [{ // ADDED: Use selectedFederalTitle for federal documents
      id: selectedFederalTitle.id,
      title: selectedFederalTitle.title,
      type: "Federal Law Title",
      filename: selectedFederalTitle.filename
    }] : documentData.federal // Fallback to mock data if no specific title is selected
  };

  if (!isLoggedIn) {
    return <LoginPage onLoginSuccess={handleLoginSuccess} />;
  }

  if (currentPage === 'landing') {
    return <LandingPage onNavigate={handleNavigate} onGoHome={handleGoHome} onLogout={handleLogout} />;
  }

  if (currentPage === 'stateSelection') {
    return <StateSelectionPage onStateSelect={handleStateSelect} onBack={handleBack} onGoHome={handleGoHome} onLogout={handleLogout} />;
  }

  if (currentPage === 'georgiaDocuments') {
    return (
      <GeorgiaDocumentSelection
        onDocumentSelect={handleGeorgiaDocumentSelect}
        onBack={handleBack}
        onGoHome={handleGoHome}
        onLogout={handleLogout}
      />
    );
  }

  if (currentPage === 'georgiaArticleSelection') {
    return (
      <GeorgiaArticleSelector
        onBack={handleBackFromArticleSelection}
        onSelectArticle={handleGeorgiaArticleSelect}
        onGoHome={handleGoHome}
        onLogout={handleLogout}
      />
    );
  }

  if (currentPage === 'federalLawSelection') { // ADDED: Render the new selector page
    return (
      <FederalLawSelector
        onBack={handleBack}
        onSelectTitle={handleFederalTitleSelect}
        onGoHome={handleGoHome}
        onLogout={handleLogout}
      />
    );
  }

  if (currentPage === 'chat' && lawType) {
    return (
      <ChatInterface
        lawType={lawType}
        onBack={handleBack}
        documentData={enhancedDocumentData}
        selectedGeorgiaDocument={selectedGeorgiaDocument}
        selectedGeorgiaArticle={selectedGeorgiaArticle}
        selectedFederalTitle={selectedFederalTitle} // ADDED: Pass the new state to ChatInterface
        onGoHome={handleGoHome}
        onLogout={handleLogout}
      />
    );
  }

  return <div>Loading...</div>;
}
