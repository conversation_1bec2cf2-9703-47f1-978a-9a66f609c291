// // src/components/GeorgiaArticleSelector.tsx
// 'use client';
// import React from 'react';
// import { ArrowLeft, FileText, ChevronRight, Scale, Users, Vote, UserCheck, MapPin, FileCheck, Computer, Mail, Settings, BarChart, AlertTriangle, Calendar, Shield } from 'lucide-react';

// interface GeorgiaArticle {
//   id: number;
//   title: string;
//   subtitle: string;
//   filename: string;
//   sections: string;
//   icon: React.ReactNode;
//   color: string;
//   lightColor: string;
//   description: string;
// }

// interface GeorgiaArticleSelectorProps {
//   onBack: () => void;
//   onSelectArticle: (article: GeorgiaArticle) => void;
//   onGoHome: () => void;
//   onLogout: () => void;
// }

// const georgiaArticles: GeorgiaArticle[] = [
//   {
//     id: 1,
//     title: "General Provisions",
//     subtitle: "Article 1",
//     filename: "Article1.txt",
//     sections: "§§ 21-2-1 — 21-2-19",
//     icon: <Scale className="w-6 h-6" />,
//     color: "from-blue-500 to-blue-600",
//     lightColor: "bg-blue-50 text-blue-800 border-blue-200",
//     description: "Fundamental definitions, purposes, and basic provisions of Georgia election law"
//   },
//   {
//     id: 2,
//     title: "Supervisory Boards and Officers",
//     subtitle: "Article 2",
//     filename: "Article2.txt",
    
//     sections: "§§ 21-2-30 — 21-2-108",
//     icon: <Users className="w-6 h-6" />,
//     color: "from-green-500 to-green-600",
//     lightColor: "bg-green-50 text-green-800 border-green-200",
//     description: "Organization and duties of election supervisory boards and election officers"
//   },
//   {
//     id: 3,
//     title: "Political Parties and Bodies",
//     subtitle: "Article 3",
//     filename: "Article3.txt",
//     sections: "§§ 21-2-110 — 21-2-113",
//     icon: <Vote className="w-6 h-6" />,
//     color: "from-purple-500 to-purple-600",
//     lightColor: "bg-purple-50 text-purple-800 border-purple-200",
//     description: "Registration and committee organization of political parties and bodies"
//   },
//   {
//     id: 4,
//     title: "Candidate Selection & Qualification",
//     subtitle: "Article 4",
//     filename: "Article4.txt",
//     sections: "§§ 21-2-130 — 21-2-187",
//     icon: <UserCheck className="w-6 h-6" />,
//     color: "from-orange-500 to-orange-600",
//     lightColor: "bg-orange-50 text-orange-800 border-orange-200",
//     description: "Requirements for candidate selection, qualification, and presidential electors"
//   },
//   {
//     id: 5,
//     title: "Presidential Preference Primary",
//     subtitle: "Article 5",
//     filename: "Article5.txt",
    
//     sections: "§§ 21-2-190 — 21-2-200",
//     icon: <Shield className="w-6 h-6" />,
//     color: "from-red-500 to-red-600",
//     lightColor: "bg-red-50 text-red-800 border-red-200",
//     description: "Procedures and requirements for presidential preference primary elections"
//   },
//   {
//     id: 6,
//     title: "Registration of Voters",
//     subtitle: "Article 6",
//     filename: "Article6.txt",
//     sections: "§§ 21-2-210 — 21-2-236",
//     icon: <UserCheck className="w-6 h-6" />,
//     color: "from-cyan-500 to-cyan-600",
//     lightColor: "bg-cyan-50 text-cyan-800 border-cyan-200",
//     description: "Voter registration processes, requirements, and maintenance procedures"
//   },
//   {
//     id: 7,
//     title: "Precincts and Polling Places",
//     subtitle: "Article 7",
//     filename: "Article7.txt",
//     sections: "§§ 21-2-260 — 21-2-270",
//     icon: <MapPin className="w-6 h-6" />,
//     color: "from-teal-500 to-teal-600",
//     lightColor: "bg-teal-50 text-teal-800 border-teal-200",
//     description: "Establishment and management of voting precincts and polling locations"
//   },
//   {
//     id: 8,
//     title: "Voting by Paper Ballot",
//     subtitle: "Article 8",
//     filename: "Article8.txt",
//     sections: "§§ 21-2-280 — 21-2-294",
//     icon: <FileCheck className="w-6 h-6" />,
//     color: "from-indigo-500 to-indigo-600",
//     lightColor: "bg-indigo-50 text-indigo-800 border-indigo-200",
//     description: "Procedures and requirements for paper ballot voting systems"
//   },
//   {
//     id: 9,
//     title: "Uniform Election Equipment",
//     subtitle: "Article 8A",
//     filename: "Article8A.txt",
//     sections: "§§ 21-2-300 — 21-2-301",
//     icon: <Settings className="w-6 h-6" />,
//     color: "from-pink-500 to-pink-600",
//     lightColor: "bg-pink-50 text-pink-800 border-pink-200",
//     description: "Standards and requirements for uniform election equipment statewide"
//   },
//   {
//     id: 10,
//     title: "Voting Machines & Vote Recorders",
//     subtitle: "Article 9",
//     filename: "Article9.txt",
//     sections: "§§ 21-2-310 — 21-2-379.26",
//     icon: <Computer className="w-6 h-6" />,
//     color: "from-emerald-500 to-emerald-600",
//     lightColor: "bg-emerald-50 text-emerald-800 border-emerald-200",
//     description: "Electronic voting machines and vote recording systems regulations"
//   },
//   {
//     id: 11,
//     title: "Absentee Voting",
//     subtitle: "Article 10",
//     filename: "Article10.txt",
//     sections: "§§ 21-2-380 — 21-2-390",
//     icon: <Mail className="w-6 h-6" />,
//     color: "from-amber-500 to-amber-600",
//     lightColor: "bg-amber-50 text-amber-800 border-amber-200",
//     description: "Absentee voting procedures, eligibility, and ballot processing"
//   },
//   {
//     id: 12,
//     title: "Election Preparation & Conduct",
//     subtitle: "Article 11",
//     filename: "Article11.txt",
//     sections: "§§ 21-2-400 — 21-2-486",
//     icon: <Settings className="w-6 h-6" />,
//     color: "from-violet-500 to-violet-600",
//     lightColor: "bg-violet-50 text-violet-800 border-violet-200",
//     description: "Comprehensive procedures for preparing and conducting elections and primaries"
//   },
//   {
//     id: 13,
//     title: "Returns",
//     subtitle: "Article 12",
//     filename: "Article12.txt",
//     sections: "§§ 21-2-490 — 21-2-504",
//     icon: <BarChart className="w-6 h-6" />,
//     color: "from-rose-500 to-rose-600",
//     lightColor: "bg-rose-50 text-rose-800 border-rose-200",
//     description: "Election result reporting, certification, and returns processing"
//   },
//   {
//     id: 14,
//     title: "Contested Elections & Primaries",
//     subtitle: "Article 13",
//     filename: "Article13.txt",
//     sections: "§§ 21-2-520 — 21-2-529",
//     icon: <AlertTriangle className="w-6 h-6" />,
//     color: "from-yellow-500 to-yellow-600",
//     lightColor: "bg-yellow-50 text-yellow-800 border-yellow-200",
//     description: "Procedures for challenging and contesting election and primary results"
//   },
//   {
//     id: 15,
//     title: "Special Elections & Primaries",
//     subtitle: "Article 14",
//     filename: "Article14.txt",
//     sections: "§§ 21-2-540 — 21-2-546",
//     icon: <Calendar className="w-6 h-6" />,
//     color: "from-lime-500 to-lime-600",
//     lightColor: "bg-lime-50 text-lime-800 border-lime-200",
//     description: "Special election and primary procedures for unexpected vacancies"
//   },
//   {
//     id: 16,
//     title: "Miscellaneous Offenses",
//     subtitle: "Article 15",
//     filename: "Article15.txt",
//     sections: "§§ 21-2-560 — 21-2-604",
//     icon: <Shield className="w-6 h-6" />,
//     color: "from-red-600 to-red-700",
//     lightColor: "bg-red-50 text-red-800 border-red-200",
//     description: "Election-related violations, penalties, and enforcement provisions"
//   },
//   // New article added here
//   // {
//   //   id: 17,
//   //   title: "Additional Provisions",
//   //   subtitle: "Article 16",
//   //   filename: "Article16.txt", // <-- Change this to your new file name
//   //   sections: "§§ 21-2-610 — 21-2-650",
//   //   icon: <Shield className="w-6 h-6" />,
//   //   color: "from-cyan-700 to-cyan-800",
//   //   lightColor: "bg-cyan-50 text-cyan-800 border-cyan-200",
//   //   description: "Placeholder for new election law provisions"
//   // }
// ];

// const GeorgiaArticleSelector: React.FC<GeorgiaArticleSelectorProps> = ({ 
//   onBack, 
//   onSelectArticle 
// }) => {
//   return (
//     <div className="min-h-screen bg-gradient-to-br from-slate-50 to-gray-100">
//       {/* Header */}
//       <div className="bg-white/80 backdrop-blur-md shadow-sm border-b border-gray-200 p-6">
//         <div className="max-w-7xl mx-auto">
//           <div className="flex items-center justify-between">
//             <div className="flex items-center space-x-4">
//               <button
//                 onClick={onBack}
//                 className="text-gray-600 hover:text-gray-900 font-medium flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors"
//               >
//                 <ArrowLeft className="w-4 h-4" />
//                 <span>Back to Selection</span>
//               </button>
              
//               <div className="flex items-center gap-3">
//                 <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-green-600 rounded-xl flex items-center justify-center text-xl shadow-lg">
//                   🏛️
//                 </div>
//                 <div>
//                   <h1 className="text-2xl font-bold text-gray-900">
//                     Georgia Election Law Articles
//                   </h1>
//                   <p className="text-gray-600">Select an article to analyze with AI</p>
//                 </div>
//               </div>
//             </div>
            
//             <div className="flex items-center gap-3">
//               <span className="text-xs bg-blue-100 text-blue-700 px-3 py-1 rounded-full font-medium">
//                 {georgiaArticles.length} Articles Available
//               </span>
//               <span className="text-xs bg-green-100 text-green-700 px-3 py-1 rounded-full font-medium">
//                 AI Analysis Ready
//               </span>
//             </div>
//           </div>
//         </div>
//       </div>

//       {/* Articles Grid */}
//       <div className="max-w-7xl mx-auto p-6">
//         <div className="mb-6">
//           <h2 className="text-lg font-semibold text-gray-900 mb-2">
//             Choose an Article to Analyze
//           </h2>
//           <p className="text-gray-600">
//             Each article covers different aspects of Georgia election law. Click on any article to start your AI-powered analysis.
//           </p>
//         </div>

//         <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
//           {georgiaArticles.map((article) => (
//             <div
//               key={article.id}
//               onClick={() => onSelectArticle(article)}
//               className="group bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-lg hover:border-gray-300 transition-all duration-200 cursor-pointer overflow-hidden"
//             >
//               {/* Article Header */}
//               <div className={`bg-gradient-to-r ${article.color} p-4 text-white`}>
//                 <div className="flex items-center justify-between mb-2">
//                   <div className="flex items-center gap-2">
//                     {article.icon}
//                     <span className="text-sm font-semibold opacity-90">
//                       {article.subtitle}
//                     </span>
//                   </div>
//                   <ChevronRight className="w-4 h-4 opacity-70 group-hover:opacity-100 transition-opacity" />
//                 </div>
//                 <h3 className="font-bold text-lg leading-tight">
//                   {article.title}
//                 </h3>
//               </div>

//               {/* Article Content */}
//               <div className="p-4">
//                 <div className={`inline-block px-2 py-1 rounded-full text-xs font-medium mb-3 ${article.lightColor}`}>
//                   {article.sections}
//                 </div>
                
//                 <p className="text-gray-600 text-sm leading-relaxed mb-4">
//                   {article.description}
//                 </p>

//                 <div className="flex items-center justify-between text-xs text-gray-500">
//                   <div className="flex items-center gap-1">
//                     <FileText className="w-3 h-3" />
//                     <span>{article.filename}</span>
//                   </div>
//                   <span className="group-hover:text-blue-600 transition-colors">
//                     Analyze →
//                   </span>
//                 </div>
//               </div>
//             </div>
//           ))}
//         </div>

//         {/* Bottom Info */}
//         <div className="mt-8 text-center">
//           <div className="bg-white rounded-lg border border-gray-200 p-6 max-w-2xl mx-auto">
//             <h3 className="font-semibold text-gray-900 mb-2">
//               🤖 AI-Powered Legal Analysis
//             </h3>
//             <p className="text-gray-600 text-sm">
//               Each article will be loaded into our AI system for detailed analysis. You can ask questions, 
//               get explanations, and explore the legal provisions with intelligent responses backed by the actual document content.
//             </p>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default GeorgiaArticleSelector;


// src/components/GeorgiaArticleSelector.tsx
'use client';
import React from 'react';
import { ArrowLeft, FileText, ChevronRight, Scale, Users, Vote, UserCheck, MapPin, FileCheck, Computer, Mail, Settings, BarChart, AlertTriangle, Calendar, Shield, LogOut } from 'lucide-react';
import Image from 'next/image';

interface GeorgiaArticle {
  id: number;
  title: string;
  subtitle: string;
  filename: string;
  sections: string;
  icon: React.ReactNode;
  color: string;
  lightColor: string;
  description: string;
}

interface GeorgiaArticleSelectorProps {
  onBack: () => void;
  onSelectArticle: (article: GeorgiaArticle) => void;
  onGoHome: () => void;
  onLogout: () => void;
}

const georgiaArticles: GeorgiaArticle[] = [
  {
    id: 1,
    title: "General Provisions",
    subtitle: "Article 1",
    filename: "Article1.txt",
    sections: "§§ 21-2-1 — 21-2-19",
    icon: <Scale className="w-6 h-6" />,
    color: "from-blue-500 to-blue-600",
    lightColor: "bg-blue-50 text-blue-800 border-blue-200",
    description: "Fundamental definitions, purposes, and basic provisions of Georgia election law"
  },
  {
    id: 2,
    title: "Supervisory Boards and Officers",
    subtitle: "Article 2",
    filename: "Article2.txt",
    
    sections: "§§ 21-2-30 — 21-2-108",
    icon: <Users className="w-6 h-6" />,
    color: "from-green-500 to-green-600",
    lightColor: "bg-green-50 text-green-800 border-green-200",
    description: "Organization and duties of election supervisory boards and election officers"
  },
  {
    id: 3,
    title: "Political Parties and Bodies",
    subtitle: "Article 3",
    filename: "Article3.txt",
    sections: "§§ 21-2-110 — 21-2-113",
    icon: <Vote className="w-6 h-6" />,
    color: "from-purple-500 to-purple-600",
    lightColor: "bg-purple-50 text-purple-800 border-purple-200",
    description: "Registration and committee organization of political parties and bodies"
  },
  {
    id: 4,
    title: "Candidate Selection & Qualification",
    subtitle: "Article 4",
    filename: "Article4.txt",
    sections: "§§ 21-2-130 — 21-2-187",
    icon: <UserCheck className="w-6 h-6" />,
    color: "from-orange-500 to-orange-600",
    lightColor: "bg-orange-50 text-orange-800 border-orange-200",
    description: "Requirements for candidate selection, qualification, and presidential electors"
  },
  {
    id: 5,
    title: "Presidential Preference Primary",
    subtitle: "Article 5",
    filename: "Article5.txt",
    
    sections: "§§ 21-2-190 — 21-2-200",
    icon: <Shield className="w-6 h-6" />,
    color: "from-red-500 to-red-600",
    lightColor: "bg-red-50 text-red-800 border-red-200",
    description: "Procedures and requirements for presidential preference primary elections"
  },
  {
    id: 6,
    title: "Registration of Voters",
    subtitle: "Article 6",
    filename: "Article6.txt",
    sections: "§§ 21-2-210 — 21-2-236",
    icon: <UserCheck className="w-6 h-6" />,
    color: "from-cyan-500 to-cyan-600",
    lightColor: "bg-cyan-50 text-cyan-800 border-cyan-200",
    description: "Voter registration processes, requirements, and maintenance procedures"
  },
  {
    id: 7,
    title: "Precincts and Polling Places",
    subtitle: "Article 7",
    filename: "Article7.txt",
    sections: "§§ 21-2-260 — 21-2-270",
    icon: <MapPin className="w-6 h-6" />,
    color: "from-teal-500 to-teal-600",
    lightColor: "bg-teal-50 text-teal-800 border-teal-200",
    description: "Establishment and management of voting precincts and polling locations"
  },
  {
    id: 8,
    title: "Voting by Paper Ballot",
    subtitle: "Article 8",
    filename: "Article8.txt",
    sections: "§§ 21-2-280 — 21-2-294",
    icon: <FileCheck className="w-6 h-6" />,
    color: "from-indigo-500 to-indigo-600",
    lightColor: "bg-indigo-50 text-indigo-800 border-indigo-200",
    description: "Procedures and requirements for paper ballot voting systems"
  },
  {
    id: 9,
    title: "Uniform Election Equipment",
    subtitle: "Article 8A",
    filename: "Article8A.txt",
    sections: "§§ 21-2-300 — 21-2-301",
    icon: <Settings className="w-6 h-6" />,
    color: "from-pink-500 to-pink-600",
    lightColor: "bg-pink-50 text-pink-800 border-pink-200",
    description: "Standards and requirements for uniform election equipment statewide"
  },
  {
    id: 10,
    title: "Voting Machines & Vote Recorders",
    subtitle: "Article 9",
    filename: "Article9.txt",
    sections: "§§ 21-2-310 — 21-2-379.26",
    icon: <Computer className="w-6 h-6" />,
    color: "from-emerald-500 to-emerald-600",
    lightColor: "bg-emerald-50 text-emerald-800 border-emerald-200",
    description: "Electronic voting machines and vote recording systems regulations"
  },
  {
    id: 11,
    title: "Absentee Voting",
    subtitle: "Article 10",
    filename: "Article10.txt",
    sections: "§§ 21-2-380 — 21-2-390",
    icon: <Mail className="w-6 h-6" />,
    color: "from-amber-500 to-amber-600",
    lightColor: "bg-amber-50 text-amber-800 border-amber-200",
    description: "Absentee voting procedures, eligibility, and ballot processing"
  },
  {
    id: 12,
    title: "Election Preparation & Conduct",
    subtitle: "Article 11",
    filename: "Article11.txt",
    sections: "§§ 21-2-400 — 21-2-486",
    icon: <Settings className="w-6 h-6" />,
    color: "from-violet-500 to-violet-600",
    lightColor: "bg-violet-50 text-violet-800 border-violet-200",
    description: "Comprehensive procedures for preparing and conducting elections and primaries"
  },
  {
    id: 13,
    title: "Returns",
    subtitle: "Article 12",
    filename: "Article12.txt",
    sections: "§§ 21-2-490 — 21-2-504",
    icon: <BarChart className="w-6 h-6" />,
    color: "from-rose-500 to-rose-600",
    lightColor: "bg-rose-50 text-rose-800 border-rose-200",
    description: "Election result reporting, certification, and returns processing"
  },
  {
    id: 14,
    title: "Contested Elections & Primaries",
    subtitle: "Article 13",
    filename: "Article13.txt",
    sections: "§§ 21-2-520 — 21-2-529",
    icon: <AlertTriangle className="w-6 h-6" />,
    color: "from-yellow-500 to-yellow-600",
    lightColor: "bg-yellow-50 text-yellow-800 border-yellow-200",
    description: "Procedures for challenging and contesting election and primary results"
  },
  {
    id: 15,
    title: "Special Elections & Primaries",
    subtitle: "Article 14",
    filename: "Article14.txt",
    sections: "§§ 21-2-540 — 21-2-546",
    icon: <Calendar className="w-6 h-6" />,
    color: "from-lime-500 to-lime-600",
    lightColor: "bg-lime-50 text-lime-800 border-lime-200",
    description: "Special election and primary procedures for unexpected vacancies"
  },
  {
    id: 16,
    title: "Miscellaneous Offenses",
    subtitle: "Article 15",
    filename: "Article15.txt",
    sections: "§§ 21-2-560 — 21-2-604",
    icon: <Shield className="w-6 h-6" />,
    color: "from-red-600 to-red-700",
    lightColor: "bg-red-50 text-red-800 border-red-200",
    description: "Election-related violations, penalties, and enforcement provisions"
  },
];

const GeorgiaArticleSelector: React.FC<GeorgiaArticleSelectorProps> = ({ 
  onBack, 
  onSelectArticle,
  onGoHome, // ADDED
  onLogout // ADDED
}) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-gray-100">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-md shadow-sm border-b border-gray-200 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={onBack}
                className="text-gray-600 hover:text-gray-900 font-medium flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>Back to Selection</span>
              </button>
              
              <div 
                className="flex items-center gap-3 cursor-pointer"
                onClick={onGoHome} // Use onGoHome here
              >
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-green-600 rounded-xl flex items-center justify-center text-xl shadow-lg">
                  🏛️
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    Georgia Election Law Articles
                  </h1>
                  <p className="text-gray-600">Select an article to analyze with AI</p>
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <button
                onClick={onLogout}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2"
              >
                <LogOut className="w-4 h-4" />
                <span>Logout</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Articles Grid */}
      <div className="max-w-7xl mx-auto p-6">
        <div className="mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-2">
            Choose an Article to Analyze
          </h2>
          <p className="text-gray-600">
            Each article covers different aspects of Georgia election law. Click on any article to start your AI-powered analysis.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {georgiaArticles.map((article) => (
            <div
              key={article.id}
              onClick={() => onSelectArticle(article)}
              className="group bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-lg hover:border-gray-300 transition-all duration-200 cursor-pointer overflow-hidden"
            >
              {/* Article Header */}
              <div className={`bg-gradient-to-r ${article.color} p-4 text-white`}>
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    {article.icon}
                    <span className="text-sm font-semibold opacity-90">
                      {article.subtitle}
                    </span>
                  </div>
                  <ChevronRight className="w-4 h-4 opacity-70 group-hover:opacity-100 transition-opacity" />
                </div>
                <h3 className="font-bold text-lg leading-tight">
                  {article.title}
                </h3>
              </div>

              {/* Article Content */}
              <div className="p-4">
                <div className={`inline-block px-2 py-1 rounded-full text-xs font-medium mb-3 ${article.lightColor}`}>
                  {article.sections}
                </div>
                
                <p className="text-gray-600 text-sm leading-relaxed mb-4">
                  {article.description}
                </p>

                <div className="flex items-center justify-between text-xs text-gray-500">
                  <div className="flex items-center gap-1">
                    <FileText className="w-3 h-3" />
                    <span>{article.filename}</span>
                  </div>
                  <span className="group-hover:text-blue-600 transition-colors">
                    Analyze →
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom Info */}
        <div className="mt-8 text-center">
          <div className="bg-white rounded-lg border border-gray-200 p-6 max-w-2xl mx-auto">
            <h3 className="font-semibold text-gray-900 mb-2">
              🤖 AI-Powered Legal Analysis
            </h3>
            <p className="text-gray-600 text-sm">
              Each article will be loaded into our AI system for detailed analysis. You can ask questions, 
              get explanations, and explore the legal provisions with intelligent responses backed by the actual document content.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GeorgiaArticleSelector;
