// src/components/FederalLawSelector.tsx
'use client';
import React from 'react';
import { ArrowLeft, FileText, ChevronRight, Gavel, Handshake, Briefcase, DollarSign, Globe, Book, Users, ClipboardList, TrendingUp, LogOut } from 'lucide-react';
import Image from 'next/image';

// Interface for a federal law title
interface FederalTitle {
  id: number;
  title: string;
  subtitle: string;
  filename: string;
  sections: string;
  icon: React.ReactNode;
  color: string;
  lightColor: string;
  description: string;
}

// Interface for the component props
interface FederalLawSelectorProps {
  onBack: () => void;
  onSelectTitle: (title: FederalTitle) => void;
  onGoHome: () => void;
  onLogout: () => void;
}

// Data array for the 9 federal law titles
const federalTitles: FederalTitle[] = [
  {
    id: 1,
    title: "PAYMENTS",
    subtitle: "Title 1",
    filename: "Title1.txt",
    sections: "§§ 101 - 106",
    icon: <Handshake className="w-6 h-6" />,
    color: "from-blue-600 to-blue-700",
    lightColor: "bg-blue-50 text-blue-800 border-blue-200",
    description: "TO STATES FOR ELECTION ADMINISTRATION IMPROVEMENTS AND REPLACEMENT OF PUNCH CARD AND LEVER VOTING MACHINES"
  },
  {
    id: 2,
    title: "COMMISSION",
    subtitle: "Title 2",
    filename: "Title2.txt",
    sections: "§§ 201 - 296",
    icon: <Users className="w-6 h-6" />,
    color: "from-green-600 to-green-700",
    lightColor: "bg-green-50 text-green-800 border-green-200",
    description: "ELECTION ASSISTANCE COMMISSION STANDARDS BOARD AND BOARD OF ADVISORS"
  },
  {
    id: 3,
    title: "ELECTION TECHNOLOGY AND ADMINISTRATION REQUIREMENTS",
    subtitle: "Title 3",
    filename: "Title3.txt",
    sections: "§§ 301 - 312",
    icon: <Briefcase className="w-6 h-6" />,
    color: "from-purple-600 to-purple-700",
    lightColor: "bg-purple-50 text-purple-800 border-purple-200",
    description: "UNIFORM AND NONDISCRIMINATORY VOTING SYSTEMS STANDARDS"
  },
  {
    id: 4,
    title: "ENFORCEMENT",
    subtitle: "Title 4",
    filename: "Title4.txt",
    sections: "§§ 401 - 402",
    icon: <DollarSign className="w-6 h-6" />,
    color: "from-orange-600 to-orange-700",
    lightColor: "bg-orange-50 text-orange-800 border-orange-200",
    description: "ACTIONS BY THE ATTORNEY GENERAL FOR DECLARATORY AND INJUNCTIVE RELIEF"
  },
  {
    id: 5,
    title: "HELP AMERICA VOTE COLLEGE PROGRAM",
    subtitle: "Title 5",
    filename: "Title5.txt",
    sections: "§§ 501 - 503",
    icon: <Globe className="w-6 h-6" />,
    color: "from-red-600 to-red-700",
    lightColor: "bg-red-50 text-red-800 border-red-200",
    description: "COORDINATION WITH INSTITUTIONS OF HIGHER EDUCATION"
  },
  {
    id: 6,
    title: "HELP AMERICA VOTE FOUNDATION",
    subtitle: "Title 6",
    filename: "Title6.txt",
    sections: "§§ 601",
    icon: <ClipboardList className="w-6 h-6" />,
    color: "from-cyan-600 to-cyan-700",
    lightColor: "bg-cyan-50 text-cyan-800 border-cyan-200",
    description: "HELP AMERICA VOTE FOUNDATION"
  },
  {
    id: 7,
    title: "VOTING RIGHTS OF MILITARY MEMBERS AND OVERSEAS CITIZENS",
    subtitle: "Title7.",
    filename: "Title7.txt",
    sections: "§§ 101 — 1332",
    icon: <Book className="w-6 h-6" />,
    color: "from-teal-600 to-teal-700",
    lightColor: "bg-teal-50 text-teal-800 border-teal-200",
    description: "VOTING ASSISTANCE PROGRAMS"
  },
  {
    id: 8,
    title: "Sarbanes–Oxley Act",
    subtitle: "Public Law 107–204",
    filename: "SarbanesOxley.txt",
    sections: "§§ 301 — 906",
    icon: <TrendingUp className="w-6 h-6" />,
    color: "from-indigo-600 to-indigo-700",
    lightColor: "bg-indigo-50 text-indigo-800 border-indigo-200",
    description: "Enhances corporate responsibility, financial disclosures, and prevents accounting fraud"
  },
  {
    id: 9,
    title: "Clean Air Act",
    subtitle: "Title 42 U.S.C. Chapter 85",
    filename: "CleanAir.txt",
    sections: "§§ 7401 — 7671q",
    icon: <Gavel className="w-6 h-6" />,
    color: "from-pink-600 to-pink-700",
    lightColor: "bg-pink-50 text-pink-800 border-pink-200",
    description: "Regulates air emissions from stationary and mobile sources"
  },
];

const FederalLawSelector: React.FC<FederalLawSelectorProps> = ({
  onBack,
  onSelectTitle, // Renamed prop to be consistent
  onGoHome,
  onLogout
}) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-gray-100">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-md shadow-sm border-b border-gray-200 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={onBack}
                className="text-gray-600 hover:text-gray-900 font-medium flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>Back to Selection</span>
              </button>

              <div
                className="flex items-center gap-3 cursor-pointer"
                onClick={onGoHome}
              >
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-green-600 rounded-xl flex items-center justify-center text-xl shadow-lg">
                  🦅
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    Federal Law Titles
                  </h1>
                  <p className="text-gray-600">Select a title to analyze with AI</p>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <button
                onClick={onLogout}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2"
              >
                <LogOut className="w-4 h-4" />
                <span>Logout</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Titles Grid */}
      <div className="max-w-7xl mx-auto p-6">
        <div className="mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-2">
            Choose a Federal Law Title to Analyze
          </h2>
          <p className="text-gray-600">
            Each title covers different aspects of federal law. Click on any title to start your AI-powered analysis.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {federalTitles.map((title) => (
            <div
              key={title.id}
              onClick={() => onSelectTitle(title)} // Use onSelectTitle here
              className="group bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-lg hover:border-gray-300 transition-all duration-200 cursor-pointer overflow-hidden"
            >
              {/* Title Header */}
              <div className={`bg-gradient-to-r ${title.color} p-4 text-white`}>
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    {title.icon}
                    <span className="text-sm font-semibold opacity-90">
                      {title.subtitle}
                    </span>
                  </div>
                  <ChevronRight className="w-4 h-4 opacity-70 group-hover:opacity-100 transition-opacity" />
                </div>
                <h3 className="font-bold text-lg leading-tight">
                  {title.title}
                </h3>
              </div>

              {/* Title Content */}
              <div className="p-4">
                <div className={`inline-block px-2 py-1 rounded-full text-xs font-medium mb-3 ${title.lightColor}`}>
                  {title.sections}
                </div>

                <p className="text-gray-600 text-sm leading-relaxed mb-4">
                  {title.description}
                </p>

                <div className="flex items-center justify-between text-xs text-gray-500">
                  <div className="flex items-center gap-1">
                    <FileText className="w-3 h-3" />
                    <span>{title.filename}</span>
                  </div>
                  <span className="group-hover:text-blue-600 transition-colors">
                    Analyze →
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom Info */}
        <div className="mt-8 text-center">
          <div className="bg-white rounded-lg border border-gray-200 p-6 max-w-2xl mx-auto">
            <h3 className="font-semibold text-gray-900 mb-2">
              🤖 AI-Powered Legal Analysis
            </h3>
            <p className="text-gray-600 text-sm">
              Each title will be loaded into our AI system for detailed analysis. You can ask questions,
              get explanations, and explore the legal provisions with intelligent responses backed by the actual document content.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FederalLawSelector;
