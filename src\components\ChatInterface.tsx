'use client';
import React, { useState, useEffect } from 'react';
import { Search, FileText, MessageCircle, Send, User, Menu, X, ArrowLeft, HelpCircle, ChevronRight } from 'lucide-react';
import { GoogleGenerativeAI } from '@google/generative-ai';

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.NEXT_PUBLIC_GEMINI_API_KEY || '');

// Type definitions
interface Document {
  id: number;
  title: string;
  type: string;
  filename?: string;
}

interface GeorgiaDocument {
  id: number;
  title: string;
  description: string;
  filename: string;
  icon: string;
  color: string;
  lightColor: string;
}

// Add the GeorgiaArticle interface with optional faqFilename and article number
interface GeorgiaArticle {
  id: number;
  title: string;
  subtitle: string;
  filename: string;
  sections: string;
  icon: React.ReactNode;
  color: string;
  lightColor: string;
  description: string;
  faqFilename?: string; // Optional custom filename
  article?: number; // Added the optional article number
}

interface Message {
  id: number;
  type: 'user' | 'ai';
  content: string;
  rating?: 'up' | 'down' | null;
  sourceDocument?: string;
  isFAQ?: boolean;
}

interface FAQ {
  question: string;
  answer: string;
}

type LawType = 'federal' | 'state' | 'county';

interface ChatInterfaceProps {
  lawType: LawType;
  onBack: () => void;
  onGoHome: () => void;
  onLogout: () => void;
  documentData: {
    [key: string]: Document[];
  };
  selectedGeorgiaDocument?: GeorgiaDocument | null;
  selectedGeorgiaArticle?: GeorgiaArticle | null;
}

// Function to format AI response text with professional styling
const formatAIResponse = (text: string): React.ReactElement => {
  const lines = text.split('\n');
  const formattedElements: React.ReactElement[] = [];
  let currentIndex = 0;

  lines.forEach((line, index) => {
    const trimmedLine = line.trim();
    
    if (!trimmedLine) {
      // Empty line - add spacing
      formattedElements.push(<div key={currentIndex++} className="h-2"></div>);
    } else if (trimmedLine.startsWith('**') && trimmedLine.endsWith('**') && trimmedLine.length > 4) {
      // Bold headers
      const headerText = trimmedLine.slice(2, -2);
      formattedElements.push(
        <h3 key={currentIndex++} className="font-bold text-gray-900 text-base mb-2 mt-4 first:mt-0">
          {headerText}
        </h3>
      );
    } else if (trimmedLine.includes('**')) {
      // Inline bold text
      const parts = trimmedLine.split('**');
      const formattedParts = parts.map((part, partIndex) => {
        if (partIndex % 2 === 1) {
          return <strong key={partIndex} className="font-semibold text-gray-900">{part}</strong>;
        }
        return part;
      });
      formattedElements.push(
        <p key={currentIndex++} className="text-gray-700 leading-relaxed mb-2">
          {formattedParts}
        </p>
      );
    } else if (trimmedLine.startsWith('• ')) {
      // Main bullet points
      const bulletText = trimmedLine.slice(2);
      formattedElements.push(
        <div key={currentIndex++} className="flex items-start gap-3 mb-2">
          <span className="text-blue-600 font-bold text-sm mt-1 flex-shrink-0">•</span>
          <span className="text-gray-700 leading-relaxed">{bulletText}</span>
        </div>
      );
    } else if (trimmedLine.startsWith('  - ')) {
      // Sub-bullet points (indented)
      const subBulletText = trimmedLine.slice(4);
      formattedElements.push(
        <div key={currentIndex++} className="flex items-start gap-3 mb-1 ml-6">
          <span className="text-gray-500 font-bold text-sm mt-1 flex-shrink-0">-</span>
          <span className="text-gray-600 leading-relaxed text-sm">{subBulletText}</span>
        </div>
      );
    } else if (trimmedLine.match(/^\d+\./)) {
      // Numbered lists
      formattedElements.push(
        <div key={currentIndex++} className="flex items-start gap-3 mb-2">
          <span className="text-blue-600 font-semibold text-sm mt-1 flex-shrink-0">
            {trimmedLine.match(/^\d+\./)?.[0]}
          </span>
          <span className="text-gray-700 leading-relaxed">
            {trimmedLine.replace(/^\d+\.\s*/, '')}
          </span>
        </div>
      );
    } else {
      // Regular paragraph text
      formattedElements.push(
        <p key={currentIndex++} className="text-gray-700 leading-relaxed mb-2">
          {trimmedLine}
        </p>
      );
    }
  });

  return <div className="space-y-1">{formattedElements}</div>;
};

const ChatInterface: React.FC<ChatInterfaceProps> = ({ 
  lawType, 
  onBack, 
  documentData, 
  selectedGeorgiaDocument,
  selectedGeorgiaArticle
}) => {
  const [documentContent, setDocumentContent] = useState<string>('');
  const [isLoadingDocument, setIsLoadingDocument] = useState<boolean>(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState<string>('');
  const [isTyping, setIsTyping] = useState<boolean>(false);
  const [faqs, setFaqs] = useState<FAQ[]>([]);
  const [isLoadingFAQ, setIsLoadingFAQ] = useState<boolean>(false);
  const [faqSidebarOpen, setFaqSidebarOpen] = useState<boolean>(true);
  const [faqFilename, setFaqFilename] = useState<string>('');

  // Determine which document to use (article takes precedence)
  const currentDocument = selectedGeorgiaArticle || selectedGeorgiaDocument;

  // Initialize welcome message and load FAQs
  useEffect(() => {
    const welcomeMessage: Message = {
      id: 1,
      type: 'ai',
      content: currentDocument 
        ? `Welcome! I've loaded "${currentDocument.title}" and I'm ready to analyze this Georgia election law document. You can browse common questions in the FAQ sidebar or ask me anything directly. What would you like to know?`
        : `Welcome! I'm here to help you navigate ${lawType} election laws. Check out the FAQ section for common questions or ask me anything directly.`,
      rating: null,
      sourceDocument: currentDocument?.title
    };
    
    setMessages([welcomeMessage]);
    
    // Load document content and corresponding FAQ if Georgia document is selected
    if (currentDocument) {
      loadDocumentContent(currentDocument.filename);
      
      let currentFaqFilename = '';
      if (selectedGeorgiaArticle?.faqFilename) {
          // If a specific faqFilename is provided in the article object, use it
          currentFaqFilename = selectedGeorgiaArticle.faqFilename;
      } else if (selectedGeorgiaArticle) {
          // NEW LOGIC: Use a regex to extract the article number from the subtitle
          const articleNumberMatch = selectedGeorgiaArticle.subtitle.match(/Article (.+)$/);
          if (articleNumberMatch && articleNumberMatch[1]) {
              currentFaqFilename = `Article${articleNumberMatch[1]}.csv`;
          } else {
              // Fallback to the old logic if parsing fails
              currentFaqFilename = `Article${selectedGeorgiaArticle.id}.csv`;
          }
      } else if (selectedGeorgiaDocument) {
          // If it's a main document, use "{id}.csv"
          currentFaqFilename = `${selectedGeorgiaDocument.id}.csv`;
      }
      
      setFaqFilename(currentFaqFilename);
      loadFAQs(currentFaqFilename);
    }
  }, [currentDocument, lawType, selectedGeorgiaArticle, selectedGeorgiaDocument]);

  // Load FAQ data from CSV
  const loadFAQs = async (filename: string): Promise<void> => {
    try {
      setIsLoadingFAQ(true);
      const response = await fetch(`/faq/${filename}`);
      if (!response.ok) {
        throw new Error(`Failed to load FAQ: ${response.statusText}`);
      }
      const csvText = await response.text();
      
      // Robust CSV parsing that handles quoted content with commas and newlines
      const faqData: FAQ[] = [];
      const rows = parseCSV(csvText);
      
      // Start from row 2 (index 1) to skip header
      for (let i = 1; i < rows.length; i++) {
        const row = rows[i];
        if (row && row.length >= 2 && row[0] && row[1]) {
          faqData.push({
            question: row[0].trim(),
            answer: row[1].trim()
          });
        }
      }
      
      setFaqs(faqData);
    } catch (error) {
      console.error('Error loading FAQs:', error);
      setFaqs([]);
    } finally {
      setIsLoadingFAQ(false);
    }
  };

  // Robust CSV parser that handles quoted content, commas, and newlines
  const parseCSV = (csvText: string): string[][] => {
    const rows: string[][] = [];
    const lines = csvText.split('\n');
    let currentRow: string[] = [];
    let currentField = '';
    let inQuotes = false;
    let i = 0;

    while (i < lines.length) {
      const line = lines[i];
      let j = 0;

      while (j < line.length) {
        const char = line[j];
        const nextChar = j + 1 < line.length ? line[j + 1] : '';

        if (char === '"') {
          if (inQuotes && nextChar === '"') {
            // Handle escaped quotes ("")
            currentField += '"';
            j += 2;
            continue;
          } else {
            // Toggle quote mode
            inQuotes = !inQuotes;
          }
        } else if (char === ',' && !inQuotes) {
          // End of field
          currentRow.push(currentField);
          currentField = '';
        } else {
          // Regular character
          currentField += char;
        }
        j++;
      }

      if (inQuotes) {
        // Multi-line field - add newline and continue to next line
        currentField += '\n';
        i++;
      } else {
        // End of row
        if (currentField || currentRow.length > 0) {
          currentRow.push(currentField);
        }
        if (currentRow.length > 0) {
          rows.push(currentRow);
        }
        currentRow = [];
        currentField = '';
        i++;
      }
    }

    // Handle last row if not completed
    if (currentRow.length > 0 || currentField) {
      if (currentField) {
        currentRow.push(currentField);
      }
      rows.push(currentRow);
    }

    return rows;
  };

  // Handle FAQ selection
  const handleFAQSelect = (faq: FAQ): void => {
    // Add user question
    const userMessage: Message = {
      id: messages.length + 1,
      type: 'user',
      content: faq.question,
      isFAQ: true
    };

    // Add AI answer
    const aiMessage: Message = {
      id: messages.length + 2,
      type: 'ai',
      content: faq.answer,
      rating: null,
      sourceDocument: currentDocument?.title,
      isFAQ: true
    };

    setMessages(prev => [...prev, userMessage, aiMessage]);
  };

  // Load document content from txt file
  const loadDocumentContent = async (filename: string): Promise<void> => {
    try {
      setIsLoadingDocument(true);
      const response = await fetch(`/documents/${filename}`);
      if (!response.ok) {
        throw new Error(`Failed to load document: ${response.statusText}`);
      }
      const content = await response.text();
      setDocumentContent(content);
    } catch (error) {
      console.error('Error loading document:', error);
      setDocumentContent(`Error loading document content. Please ensure ${filename} exists in the /public/documents/ folder.`);
    } finally {
      setIsLoadingDocument(false);
    }
  };

  // Enhanced Gemini AI integration with document content and professional formatting
  const generateAIResponse = async (userMessage: string): Promise<string> => {
    try {
      const model = genAI.getGenerativeModel({ 
        model: "gemini-2.0-flash-lite",
        generationConfig: {
          temperature: 0.2,
        }
      });
      
      // Build conversation history
      const conversationHistory = messages
        .filter(msg => msg.id !== 1)
        .map(msg => `${msg.type === 'user' ? 'User' : 'Assistant'}: ${msg.content}`)
        .join('\n');
      
      // Document context with actual content for Georgia documents
      const documentContext = currentDocument && documentContent
        ? `SELECTED DOCUMENT: "${currentDocument.title}" (Georgia State Law)
DOCUMENT CONTENT:
${documentContent}

`
        : '';
      
      const prompt = `You are a legal AI assistant specializing in Georgia election law. You have access to specific legal document content and should provide accurate answers with source citations.

${documentContext}CONVERSATION CONTEXT:
Law Type: ${lawType.toUpperCase()} Election Laws - Georgia
Current Document: ${currentDocument?.title || 'No specific document'}

${conversationHistory ? `PREVIOUS CONVERSATION:
${conversationHistory}

` : ''}CURRENT USER QUESTION: ${userMessage}

FORMATTING REQUIREMENTS - VERY IMPORTANT:
Your response MUST be formatted using these exact markdown patterns:

**Bold Text** - Use for main headings, subheadings, and important terms
• Bullet Point - Use for lists (with bullet symbol •)
  - Sub-bullet - Use for sub-points (with dash and 2 spaces indent)

Example format:
**Main Topic/Heading**

Brief introduction paragraph.

**Key Requirements:**
• First requirement with details
• Second requirement with details
  - Sub-requirement or detail
  - Another sub-detail
• Third requirement

**Important Provisions:**
• Provision one [Source: Document - Section X]
• Provision two [Source: Document - Section Y]

**Legal Disclaimer:** This information is for educational purposes only and does not constitute legal advice.

CONTENT INSTRUCTIONS:
1. Answer based PRIMARILY on the provided document content above
2. Maintain conversation continuity and reference previous topics when relevant  
3. ALWAYS provide source citations when referencing specific parts of the document
4. Use this format for citations: [Source: ${currentDocument?.title || 'Document'} - specific section/provision]
5. If the document doesn't contain information to answer the question, acknowledge this limitation clearly
6. Provide accurate information about Georgia election laws
7. Include appropriate legal disclaimers
8. Use clear, understandable language
9. Structure your response with clear headings and bullet points
10. Be professional and comprehensive

Response with professional formatting and source citations:`;

      const result = await model.generateContent(prompt);
      const response = await result.response;
      return response.text();
    } catch (error) {
      console.error('Error generating AI response:', error);
      return `I apologize, but I'm having trouble connecting to the AI service right now. For questions about "${userMessage}" regarding ${currentDocument?.title || 'Georgia election law'}, please consult the original legal documents or seek qualified legal counsel.`;
    }
  };

  // Message sending
  const handleSendMessage = async (): Promise<void> => {
    if (!inputMessage.trim()) return;

    const userMessage: Message = {
      id: messages.length + 1,
      type: 'user',
      content: inputMessage,
      sourceDocument: currentDocument?.title
    };

    setMessages(prev => [...prev, userMessage]);
    const currentInput = inputMessage;
    setInputMessage('');
    setIsTyping(true);

    try {
      const aiResponseText = await generateAIResponse(currentInput);
      
      const aiResponse: Message = {
        id: messages.length + 2,
        type: 'ai',
        content: aiResponseText,
        rating: null,
        sourceDocument: currentDocument?.title
      };
      
      setMessages(prev => [...prev, aiResponse]);
    } catch (error) {
      console.error('Error in AI response:', error);
      
      const fallbackResponse: Message = {
        id: messages.length + 2,
        type: 'ai',
        content: `I apologize for the technical difficulty. For questions about "${currentInput}" regarding ${currentDocument?.title || 'Georgia election law'}, please consult the original legal documents.`,
        rating: null
      };
      
      setMessages(prev => [...prev, fallbackResponse]);
    } finally {
      setIsTyping(false);
    }
  };

  const getTypeColor = (): string => {
    if (currentDocument) {
      return currentDocument.color.replace('from-', 'bg-').replace(' to-blue-600', '').replace(' to-green-600', '').replace(' to-purple-600', '').replace(' to-orange-600', '');
    }
    switch (lawType) {
      case 'federal': return 'bg-blue-600';
      case 'state': return 'bg-green-600';
      case 'county': return 'bg-purple-600';
      default: return 'bg-gray-600';
    }
  };

  const getTypeColorLight = (): string => {
    if (currentDocument) {
      return currentDocument.lightColor;
    }
    switch (lawType) {
      case 'federal': return 'bg-blue-50 text-blue-800 border-blue-200';
      case 'state': return 'bg-green-50 text-green-800 border-green-200';
      case 'county': return 'bg-purple-50 text-purple-800 border-purple-200';
      default: return 'bg-gray-50 text-gray-800 border-gray-200';
    }
  };

  const getGradientColor = (): string => {
    if (currentDocument) {
      return currentDocument.color;
    }
    switch (lawType) {
      case 'federal': return 'from-blue-500 to-blue-600';
      case 'state': return 'from-green-500 to-green-600';
      case 'county': return 'from-purple-500 to-purple-600';
      default: return 'from-gray-500 to-gray-600';
    }
  };

  const handleRating = (messageId: number, rating: 'up' | 'down'): void => {
    setMessages(prev => prev.map(msg => 
      msg.id === messageId 
        ? { ...msg, rating: msg.rating === rating ? null : rating }
        : msg
    ));
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>): void => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="h-screen bg-gradient-to-br from-slate-50 to-gray-100 flex">
      {/* FAQ Sidebar */}
      <div className={`${faqSidebarOpen ? 'w-80' : 'w-0'} transition-all duration-300 bg-white/90 backdrop-blur-sm shadow-xl border-r border-gray-200 overflow-hidden flex-shrink-0`}>
        <div className="p-4 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-white">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-lg font-bold text-gray-900 flex items-center gap-2">
              <HelpCircle className="w-5 h-5 text-blue-600" />
              FAQs: {currentDocument?.title || 'Questions'}
            </h3>
            <button
              onClick={() => setFaqSidebarOpen(false)}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
          <div className={`px-3 py-2 rounded-full text-xs font-semibold border ${getTypeColorLight()}`}>
            {isLoadingFAQ ? (
              <span>📥 Loading FAQs...</span>
            ) : (
              <span>❓ {faqs.length} questions • {faqFilename || 'No file'}</span>
            )}
          </div>
        </div>

        <div className="p-4 space-y-2 max-h-full overflow-y-auto">
          {isLoadingFAQ ? (
            <div className="text-center py-8">
              <div className="animate-spin w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full mx-auto mb-2"></div>
              <p className="text-gray-500 text-sm">Loading FAQs...</p>
            </div>
          ) : faqs.length > 0 ? (
            faqs.map((faq, index) => (
              <div
                key={index}
                onClick={() => handleFAQSelect(faq)}
                className="p-3 rounded-lg cursor-pointer transition-all duration-200 border border-gray-200 hover:border-blue-300 hover:bg-blue-50 hover:shadow-md group"
              >
                <div className="flex items-start gap-2">
                  <span className="text-blue-600 font-bold text-xs mt-1 flex-shrink-0">Q{index + 1}</span>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-800 leading-relaxed group-hover:text-blue-900">
                      {faq.question}
                    </p>
                  </div>
                  <ChevronRight className="w-4 h-4 text-gray-400 group-hover:text-blue-600 transition-colors flex-shrink-0 mt-0.5" />
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8">
              <HelpCircle className="w-12 h-12 text-gray-300 mx-auto mb-2" />
              <p className="text-gray-500 text-sm">No FAQs available</p>
              <p className="text-gray-400 text-xs mt-1">
                Please place the file `**{faqFilename}**` in the `/public/faq/` folder.
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Header - Fixed */}
        <div className="bg-white/80 backdrop-blur-md shadow-sm border-b border-gray-200 p-4 flex-shrink-0">
          <div className="flex items-center justify-between max-w-7xl mx-auto">
            <div className="flex items-center space-x-4">
              {!faqSidebarOpen && (
                <button
                  onClick={() => setFaqSidebarOpen(true)}
                  className="p-2 hover:bg-gray-100 rounded-xl transition-colors"
                >
                  <Menu className="w-5 h-5" />
                </button>
              )}
              
              <button
                onClick={onBack}
                className="text-gray-600 hover:text-gray-900 font-medium flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>Back</span>
              </button>
              
              {currentDocument && (
                <div className="flex items-center gap-3">
                  <div className={`w-12 h-12 bg-gradient-to-r ${currentDocument.color} rounded-xl flex items-center justify-center text-xl shadow-lg`}>
                    {typeof currentDocument.icon === 'string' ? currentDocument.icon : '📄'}
                  </div>
                  <div>
                    <h2 className="text-xl font-bold text-gray-900">
                      {currentDocument.title}
                    </h2>
                    <p className="text-sm text-gray-600">
                      {selectedGeorgiaArticle ? 'Georgia Election Law Article' : 'Georgia Election Law Analysis'}
                    </p>
                  </div>
                </div>
              )}
              
              {!currentDocument && (
                <div className="flex items-center gap-3">
                  <div className={`w-4 h-4 rounded-full ${getTypeColor()}`}>
                    <div className="w-full h-full rounded-full animate-pulse"></div>
                  </div>
                  <h2 className="text-xl font-bold text-gray-900 capitalize">
                    {lawType} Election Laws AI
                  </h2>
                </div>
              )}
            </div>
            
            <div className="flex items-center gap-3">
              <span className="text-xs bg-green-100 text-green-700 px-3 py-1 rounded-full font-medium">
                Document Analysis
              </span>
              <span className="text-xs bg-blue-100 text-blue-700 px-3 py-1 rounded-full font-medium">
                FAQ Ready
              </span>
              {isLoadingDocument && (
                <span className="text-xs bg-orange-100 text-orange-700 px-3 py-1 rounded-full font-medium">
                  Loading Document...
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Messages Area - Scrollable */}
        <div className="flex-1 overflow-y-auto p-6 max-w-5xl mx-auto w-full">
          <div className="space-y-6 pb-4">
            {messages.map((message: Message) => (
              <div
                key={message.id}
                className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'} animate-fadeIn`}
              >
                <div className="flex items-start gap-3 max-w-4xl">
                  {message.type === 'ai' && (
                    <div className={`w-10 h-10 rounded-full bg-gradient-to-r ${getGradientColor()} flex items-center justify-center shadow-lg flex-shrink-0`}>
                      <span className="text-white font-bold text-sm">AI</span>
                    </div>
                  )}
                  <div className="flex flex-col">
                    <div
                      className={`px-6 py-4 rounded-2xl shadow-lg ${
                        message.type === 'user'
                          ? `bg-gradient-to-r ${getGradientColor()} text-white ml-auto`
                          : 'bg-white text-gray-900 border border-gray-100'
                      } ${message.isFAQ ? 'border-l-4 border-l-blue-400' : ''}`}
                    >
                      {message.isFAQ && message.type === 'user' && (
                        <div className="mb-2 text-xs font-semibold text-blue-100 opacity-80">
                          📋 FAQ Question
                        </div>
                      )}
                      
                      {message.isFAQ && message.type === 'ai' && (
                        <div className="mb-3 text-xs font-semibold text-blue-700 bg-blue-50 px-2 py-1 rounded">
                          📋 FAQ Answer
                        </div>
                      )}
                      
                      {message.type === 'ai' ? (
                        <div className="text-sm leading-relaxed">
                          {formatAIResponse(message.content)}
                        </div>
                      ) : (
                        <p className="text-sm leading-relaxed whitespace-pre-wrap">{message.content}</p>
                      )}
                      
                      {/* Source citation for AI messages */}
                      {message.type === 'ai' && message.sourceDocument && !message.isFAQ && (
                        <div className="mt-4 p-3 bg-gray-50 rounded-lg border-l-4 border-blue-400">
                          <div className="text-xs text-gray-600 flex items-center gap-2">
                            <FileText className="w-4 h-4" />
                            <span className="font-medium">Source Document:</span> 
                            <span className="text-blue-700 font-semibold">{message.sourceDocument}</span>
                          </div>
                        </div>
                      )}
                      
                      <div className="text-xs opacity-70 mt-3 pt-2 border-t border-gray-100">
                        {new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </div>
                    </div>
                    
                    {/* Rating Buttons */}
                    {message.type === 'ai' && (
                      <div className="flex items-center gap-2 mt-2 ml-2">
                        <span className="text-xs text-gray-500">Rate this response:</span>
                        <div className="flex gap-1">
                          <button
                            onClick={() => handleRating(message.id, 'up')}
                            className={`p-2 rounded-lg transition-all duration-200 ${
                              message.rating === 'up'
                                ? 'bg-green-100 text-green-600 shadow-md'
                                : 'text-gray-400 hover:text-green-600 hover:bg-green-50'
                            }`}
                          >
                            👍
                          </button>
                          <button
                            onClick={() => handleRating(message.id, 'down')}
                            className={`p-2 rounded-lg transition-all duration-200 ${
                              message.rating === 'down'
                                ? 'bg-red-100 text-red-600 shadow-md'
                                : 'text-gray-400 hover:text-red-600 hover:bg-red-50'
                            }`}
                          >
                            👎
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                  {message.type === 'user' && (
                    <div className="w-10 h-10 rounded-full bg-gradient-to-r from-gray-400 to-gray-500 flex items-center justify-center shadow-lg flex-shrink-0">
                      <User className="w-5 h-5 text-white" />
                    </div>
                  )}
                </div>
              </div>
            ))}
            
            {/* Typing Indicator */}
            {isTyping && (
              <div className="flex justify-start animate-fadeIn">
                <div className="flex items-start gap-3 max-w-2xl">
                  <div className={`w-10 h-10 rounded-full bg-gradient-to-r ${getGradientColor()} flex items-center justify-center shadow-lg`}>
                    <span className="text-white font-bold text-sm">AI</span>
                  </div>
                  <div className="bg-white text-gray-900 border border-gray-100 px-6 py-4 rounded-2xl shadow-lg">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                    <div className="text-xs text-gray-500 mt-2">Analyzing document content...</div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Input Area - Fixed at Bottom */}
        <div className="bg-white/80 backdrop-blur-md border-t border-gray-200 p-6 flex-shrink-0">
          <div className="flex items-center space-x-4 max-w-4xl mx-auto">
            <div className="flex-1 relative">
              <input
                type="text"
                value={inputMessage}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setInputMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder={`Ask about ${currentDocument?.title || 'Georgia election laws'}...`}
                className="w-full border-2 border-gray-200 bg-white rounded-2xl px-6 py-4 text-gray-900 placeholder-gray-500 focus:outline-none focus:border-gray-400 focus:ring-4 focus:ring-gray-100 transition-all text-base shadow-sm"
                disabled={isTyping}
              />
              <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400">
                <MessageCircle className="w-5 h-5" />
              </div>
            </div>
            <button
              onClick={handleSendMessage}
              disabled={!inputMessage.trim() || isTyping}
              className={`bg-gradient-to-r ${getGradientColor()} hover:scale-105 disabled:hover:scale-100 text-white px-8 py-4 rounded-2xl transition-all duration-200 flex items-center space-x-2 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed font-semibold`}
            >
              <Send className="w-5 h-5" />
              <span>{isTyping ? 'Analyzing...' : 'Send'}</span>
            </button>
          </div>
          <div className="text-center mt-2">
            <span className="text-xs text-gray-500">
              Powered by Document Analysis + Gemini AI • Browse FAQs or ask directly
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatInterface;
